# 数学训练网页设计文档

## 概述

数学训练网页是一个单页面应用（SPA），使用纯前端技术实现，无需后端服务器。应用采用模块化设计，包含练习模块、错题管理模块、进度跟踪模块和奖励系统模块。数据通过浏览器本地存储（localStorage）持久化保存。

## 架构

### 整体架构
```
┌─────────────────────────────────────┐
│           用户界面层 (UI Layer)        │
├─────────────────────────────────────┤
│         业务逻辑层 (Logic Layer)       │
├─────────────────────────────────────┤
│         数据管理层 (Data Layer)        │
├─────────────────────────────────────┤
│        本地存储层 (Storage Layer)      │
└─────────────────────────────────────┘
```

### 技术栈
- **前端框架**: 原生 HTML5 + CSS3 + JavaScript (ES6+)
- **样式框架**: 自定义 CSS，响应式设计
- **数据存储**: localStorage API
- **图表库**: Chart.js (用于进度可视化)
- **动画效果**: CSS3 Transitions + JavaScript

## 组件和接口

### 核心组件

#### 1. 应用主控制器 (AppController)
```javascript
class AppController {
  constructor()
  init()
  switchView(viewName)
  handleNavigation()
}
```

#### 2. 练习管理器 (ExerciseManager)
```javascript
class ExerciseManager {
  generateQuestions(level, count)
  checkAnswer(questionId, answer)
  calculateScore()
  getNextQuestion()
}
```

#### 3. 题库管理器 (QuestionBankManager)
```javascript
class QuestionBankManager {
  getAvailableBanks()
  loadQuestionBank(bankId)
  generateQuestion(type, difficulty)
}
```

#### 4. 错题管理器 (WrongQuestionManager)
```javascript
class WrongQuestionManager {
  addWrongQuestion(question, userAnswer)
  getWrongQuestions()
  markAsCorrect(questionId)
  generateReviewSession()
}
```

#### 5. 进度跟踪器 (ProgressTracker)
```javascript
class ProgressTracker {
  recordSession(sessionData)
  getProgressData()
  calculateStatistics()
  updateAchievements()
}
```

#### 6. 奖励系统 (RewardSystem)
```javascript
class RewardSystem {
  calculatePoints(correctAnswers, timeSpent)
  checkAchievements(userData)
  unlockRewards(points)
  displayCelebration()
}
```

### 用户界面组件

#### 1. 主菜单界面 (MainMenu)
- 难度级别选择
- 题库选择
- 错题复训入口
- 进度查看入口
- 设置选项

#### 2. 练习界面 (ExerciseView)
- 题目显示区域
- 答案输入区域
- 进度指示器
- 计时器
- 提示按钮

#### 3. 结果界面 (ResultView)
- 成绩统计
- 错题回顾
- 奖励展示
- 继续练习选项

#### 4. 错题本界面 (WrongQuestionView)
- 错题列表
- 筛选选项
- 复训开始按钮
- 删除选项

#### 5. 进度界面 (ProgressView)
- 成绩图表
- 成就展示
- 统计数据
- 历史记录

## 数据模型

### 用户数据模型
```javascript
const UserProfile = {
  id: String,
  name: String,
  level: Number,
  totalPoints: Number,
  achievements: Array,
  unlockedLevels: Array,
  createdAt: Date,
  lastActiveAt: Date
}
```

### 练习会话模型
```javascript
const ExerciseSession = {
  id: String,
  userId: String,
  level: String,
  questionBank: String,
  questions: Array,
  answers: Array,
  startTime: Date,
  endTime: Date,
  score: Number,
  correctCount: Number,
  totalCount: Number
}
```

### 题目模型
```javascript
const Question = {
  id: String,
  type: String, // 'addition', 'subtraction', 'multiplication'
  operand1: Number,
  operand2: Number,
  correctAnswer: Number,
  difficulty: Number,
  category: String
}
```

### 错题模型
```javascript
const WrongQuestion = {
  id: String,
  question: Question,
  userAnswer: Number,
  correctAnswer: Number,
  wrongCount: Number,
  lastWrongAt: Date,
  masteredAt: Date
}
```

### 成就模型
```javascript
const Achievement = {
  id: String,
  name: String,
  description: String,
  icon: String,
  condition: Object,
  points: Number,
  unlockedAt: Date
}
```

## 错误处理

### 输入验证
- 答案输入格式验证
- 数值范围检查
- 空值处理

### 数据存储错误
- localStorage 容量限制处理
- 数据损坏恢复机制
- 浏览器兼容性检查

### 用户体验错误
- 网络连接检查（如果需要加载外部资源）
- 页面加载失败处理
- 操作超时处理

## 测试策略

### 单元测试
- 题目生成算法测试
- 答案验证逻辑测试
- 积分计算测试
- 数据存储操作测试

### 集成测试
- 组件间交互测试
- 数据流测试
- 用户操作流程测试

### 用户体验测试
- 响应式设计测试
- 性能测试
- 可访问性测试
- 跨浏览器兼容性测试

### 测试数据
- 模拟用户数据
- 边界值测试数据
- 异常情况测试数据

## 实现细节

### 题目生成策略
1. **10以内加减法**: 操作数范围 0-10，结果非负
2. **20以内加减法**: 操作数范围 0-20，结果非负
3. **100以内加减法**: 操作数范围 0-100，结果非负
4. **乘法表**: 操作数范围 1-9，标准九九乘法表

### 难度递进算法
- 前10题：基础难度
- 第11-30题：中等难度
- 第31-50题：当前级别最高难度
- 错题权重：错题出现频率提高

### 奖励机制设计
- **基础积分**: 每答对1题 = 10分
- **速度奖励**: 快速答题额外奖励
- **连击奖励**: 连续答对奖励递增
- **完成奖励**: 完成一轮练习额外奖励
- **成就系统**: 特定条件解锁称号和头像

### 本地存储策略
- 用户数据：`mathTraining_user`
- 练习记录：`mathTraining_sessions`
- 错题数据：`mathTraining_wrongQuestions`
- 设置数据：`mathTraining_settings`