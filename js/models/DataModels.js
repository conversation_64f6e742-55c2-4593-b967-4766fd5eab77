/**
 * 数据模型定义
 * 包含所有核心数据结构和验证方法
 */

// 用户数据模型
class UserProfile {
    constructor(data = {}) {
        this.id = data.id || this.generateId();
        this.name = data.name || '学生';
        this.level = data.level || 1;
        this.totalPoints = data.totalPoints || 0;
        this.achievements = data.achievements || [];
        this.unlockedLevels = data.unlockedLevels || ['within-10'];
        this.createdAt = data.createdAt || new Date();
        this.lastActiveAt = data.lastActiveAt || new Date();
    }

    generateId() {
        return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    validate() {
        const errors = [];
        if (!this.id) errors.push('用户ID不能为空');
        if (!this.name) errors.push('用户名不能为空');
        if (this.level < 1) errors.push('用户等级必须大于0');
        if (this.totalPoints < 0) errors.push('总积分不能为负数');
        if (!Array.isArray(this.achievements)) errors.push('成就必须是数组');
        if (!Array.isArray(this.unlockedLevels)) errors.push('解锁级别必须是数组');
        return errors;
    }

    toJSON() {
        return {
            id: this.id,
            name: this.name,
            level: this.level,
            totalPoints: this.totalPoints,
            achievements: this.achievements,
            unlockedLevels: this.unlockedLevels,
            createdAt: this.createdAt,
            lastActiveAt: this.lastActiveAt
        };
    }
}

// 题目模型
class Question {
    constructor(data = {}) {
        this.id = data.id || this.generateId();
        this.type = data.type || 'addition'; // addition, subtraction, multiplication, division
        this.operand1 = data.operand1 || 0;
        this.operand2 = data.operand2 || 0;
        this.correctAnswer = data.correctAnswer || this.calculateAnswer();
        this.difficulty = data.difficulty || 1;
        this.category = data.category || 'within-10';
    }

    generateId() {
        return 'question_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    calculateAnswer() {
        switch (this.type) {
            case 'addition':
                return this.operand1 + this.operand2;
            case 'subtraction':
                return this.operand1 - this.operand2;
            case 'multiplication':
                return this.operand1 * this.operand2;
            case 'division':
                return Math.floor(this.operand1 / this.operand2);
            default:
                return 0;
        }
    }

    getExpression() {
        const operators = {
            'addition': '+',
            'subtraction': '-',
            'multiplication': '×',
            'division': '÷'
        };
        return `${this.operand1} ${operators[this.type]} ${this.operand2}`;
    }

    validate() {
        const errors = [];
        if (!this.id) errors.push('题目ID不能为空');
        if (!['addition', 'subtraction', 'multiplication', 'division'].includes(this.type)) {
            errors.push('题目类型无效');
        }
        if (typeof this.operand1 !== 'number') errors.push('操作数1必须是数字');
        if (typeof this.operand2 !== 'number') errors.push('操作数2必须是数字');
        if (typeof this.correctAnswer !== 'number') errors.push('正确答案必须是数字');
        if (this.difficulty < 1) errors.push('难度等级必须大于0');
        return errors;
    }

    toJSON() {
        return {
            id: this.id,
            type: this.type,
            operand1: this.operand1,
            operand2: this.operand2,
            correctAnswer: this.correctAnswer,
            difficulty: this.difficulty,
            category: this.category
        };
    }
}

// 练习会话模型
class ExerciseSession {
    constructor(data = {}) {
        this.id = data.id || this.generateId();
        this.userId = data.userId || '';
        this.level = data.level || 'within-10';
        this.questionBank = data.questionBank || 'default';
        this.questions = data.questions || [];
        this.answers = data.answers || [];
        this.startTime = data.startTime || new Date();
        this.endTime = data.endTime || null;
        this.score = data.score || 0;
        this.correctCount = data.correctCount || 0;
        this.totalCount = data.totalCount || 0;
    }

    generateId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    addAnswer(questionId, userAnswer, isCorrect, timeSpent = 0) {
        this.answers.push({
            questionId,
            userAnswer,
            isCorrect,
            timeSpent,
            timestamp: new Date()
        });
        
        if (isCorrect) {
            this.correctCount++;
        }
        this.totalCount = this.answers.length;
    }

    calculateAccuracy() {
        if (this.totalCount === 0) return 0;
        return Math.round((this.correctCount / this.totalCount) * 100);
    }

    getTotalTimeSpent() {
        if (!this.endTime) return 0;
        return Math.floor((this.endTime - this.startTime) / 1000);
    }

    complete() {
        this.endTime = new Date();
        this.score = this.calculateScore();
    }

    calculateScore() {
        const baseScore = this.correctCount * 10;
        const accuracyBonus = this.calculateAccuracy() >= 80 ? 50 : 0;
        const speedBonus = this.calculateSpeedBonus();
        return baseScore + accuracyBonus + speedBonus;
    }

    calculateSpeedBonus() {
        const avgTimePerQuestion = this.getTotalTimeSpent() / this.totalCount;
        if (avgTimePerQuestion <= 5) return 30;
        if (avgTimePerQuestion <= 10) return 20;
        if (avgTimePerQuestion <= 15) return 10;
        return 0;
    }

    validate() {
        const errors = [];
        if (!this.id) errors.push('会话ID不能为空');
        if (!this.userId) errors.push('用户ID不能为空');
        if (!this.level) errors.push('级别不能为空');
        if (!Array.isArray(this.questions)) errors.push('题目列表必须是数组');
        if (!Array.isArray(this.answers)) errors.push('答案列表必须是数组');
        if (!(this.startTime instanceof Date)) errors.push('开始时间必须是日期对象');
        return errors;
    }

    toJSON() {
        return {
            id: this.id,
            userId: this.userId,
            level: this.level,
            questionBank: this.questionBank,
            questions: this.questions,
            answers: this.answers,
            startTime: this.startTime,
            endTime: this.endTime,
            score: this.score,
            correctCount: this.correctCount,
            totalCount: this.totalCount
        };
    }
}

// 错题模型
class WrongQuestion {
    constructor(data = {}) {
        this.id = data.id || this.generateId();
        this.question = data.question instanceof Question ? data.question : new Question(data.question);
        this.userAnswer = data.userAnswer || 0;
        this.correctAnswer = data.correctAnswer || this.question.correctAnswer;
        this.wrongCount = data.wrongCount || 1;
        this.lastWrongAt = data.lastWrongAt || new Date();
        this.masteredAt = data.masteredAt || null;
        this.correctStreak = data.correctStreak || 0;
    }

    generateId() {
        return 'wrong_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    markCorrect() {
        this.correctStreak++;
        if (this.correctStreak >= 3) {
            this.masteredAt = new Date();
        }
    }

    markWrong() {
        this.wrongCount++;
        this.correctStreak = 0;
        this.lastWrongAt = new Date();
    }

    isMastered() {
        return this.masteredAt !== null;
    }

    validate() {
        const errors = [];
        if (!this.id) errors.push('错题ID不能为空');
        if (!(this.question instanceof Question)) errors.push('题目必须是Question实例');
        if (typeof this.userAnswer !== 'number') errors.push('用户答案必须是数字');
        if (typeof this.correctAnswer !== 'number') errors.push('正确答案必须是数字');
        if (this.wrongCount < 1) errors.push('错误次数必须大于0');
        return errors;
    }

    toJSON() {
        return {
            id: this.id,
            question: this.question.toJSON(),
            userAnswer: this.userAnswer,
            correctAnswer: this.correctAnswer,
            wrongCount: this.wrongCount,
            lastWrongAt: this.lastWrongAt,
            masteredAt: this.masteredAt,
            correctStreak: this.correctStreak
        };
    }
}

// 成就模型
class Achievement {
    constructor(data = {}) {
        this.id = data.id || '';
        this.name = data.name || '';
        this.description = data.description || '';
        this.icon = data.icon || '🏆';
        this.condition = data.condition || {};
        this.points = data.points || 0;
        this.unlockedAt = data.unlockedAt || null;
    }

    isUnlocked() {
        return this.unlockedAt !== null;
    }

    unlock() {
        if (!this.isUnlocked()) {
            this.unlockedAt = new Date();
        }
    }

    validate() {
        const errors = [];
        if (!this.id) errors.push('成就ID不能为空');
        if (!this.name) errors.push('成就名称不能为空');
        if (!this.description) errors.push('成就描述不能为空');
        if (typeof this.condition !== 'object') errors.push('成就条件必须是对象');
        if (this.points < 0) errors.push('成就积分不能为负数');
        return errors;
    }

    toJSON() {
        return {
            id: this.id,
            name: this.name,
            description: this.description,
            icon: this.icon,
            condition: this.condition,
            points: this.points,
            unlockedAt: this.unlockedAt
        };
    }
}

// 导出所有模型类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        UserProfile,
        Question,
        ExerciseSession,
        WrongQuestion,
        Achievement
    };
}