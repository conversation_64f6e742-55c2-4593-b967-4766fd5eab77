/**
 * 应用入口文件
 * 初始化应用并启动主控制器
 */

// 应用配置
const APP_CONFIG = {
    version: '1.0.0',
    name: '数学训练',
    defaultSettings: {
        soundEnabled: true,
        animationEnabled: true,
        theme: 'default',
        difficulty: 'normal'
    },
    levels: {
        'within-10': {
            name: '10以内加减法',
            description: '适合初学者的基础练习',
            unlocked: true
        },
        'within-20': {
            name: '20以内加减法',
            description: '进阶练习，需要80%正确率解锁',
            unlocked: false
        },
        'within-100': {
            name: '100以内加减法',
            description: '高级练习，需要80%正确率解锁',
            unlocked: false
        },
        'multiplication': {
            name: '乘法表',
            description: '九九乘法表练习，需要80%正确率解锁',
            unlocked: false
        }
    }
};

// 全局变量
let appController;

/**
 * 应用初始化
 */
function initializeApp() {
    try {
        console.log(`${APP_CONFIG.name} v${APP_CONFIG.version} 正在启动...`);
        
        // 检查浏览器兼容性
        if (!checkBrowserCompatibility()) {
            showError('您的浏览器版本过低，请升级后再试');
            return;
        }
        
        // 初始化存储管理器
        if (!storageManager.isSupported) {
            console.warn('localStorage不支持，部分功能可能受限');
        }
        
        // 初始化用户数据
        initializeUserData();
        
        // 创建应用控制器
        appController = new AppController();
        
        // 启动应用
        appController.init();
        
        console.log('应用启动成功');
        
    } catch (error) {
        console.error('应用初始化失败:', error);
        showError('应用启动失败，请刷新页面重试');
    }
}

/**
 * 检查浏览器兼容性
 */
function checkBrowserCompatibility() {
    const compatibility = {
        supported: true,
        warnings: [],
        features: {}
    };
    
    // 检查必需的API支持
    const requiredFeatures = {
        localStorage: 'localStorage' in window,
        JSON: 'JSON' in window,
        addEventListener: 'addEventListener' in window,
        querySelector: 'querySelector' in document,
        ES6: (() => {
            try {
                eval('const test = () => {}; class Test {}');
                return true;
            } catch (e) {
                return false;
            }
        })(),
        flexbox: CSS.supports('display', 'flex'),
        grid: CSS.supports('display', 'grid')
    };
    
    // 检查可选功能
    const optionalFeatures = {
        webAudio: 'AudioContext' in window || 'webkitAudioContext' in window,
        canvas: 'HTMLCanvasElement' in window,
        webGL: (() => {
            try {
                const canvas = document.createElement('canvas');
                return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
            } catch (e) {
                return false;
            }
        })(),
        serviceWorker: 'serviceWorker' in navigator,
        performance: 'performance' in window && 'now' in performance
    };
    
    // 检查必需功能
    Object.entries(requiredFeatures).forEach(([feature, supported]) => {
        compatibility.features[feature] = supported;
        if (!supported) {
            compatibility.supported = false;
            compatibility.warnings.push(`不支持 ${feature}`);
        }
    });
    
    // 检查可选功能
    Object.entries(optionalFeatures).forEach(([feature, supported]) => {
        compatibility.features[feature] = supported;
        if (!supported) {
            compatibility.warnings.push(`不支持 ${feature} (可选功能)`);
        }
    });
    
    // 浏览器检测
    const userAgent = navigator.userAgent;
    const browserInfo = {
        isChrome: /Chrome/.test(userAgent) && /Google Inc/.test(navigator.vendor),
        isFirefox: /Firefox/.test(userAgent),
        isSafari: /Safari/.test(userAgent) && /Apple Computer/.test(navigator.vendor),
        isEdge: /Edge/.test(userAgent),
        isIE: /Trident/.test(userAgent),
        isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)
    };
    
    compatibility.browser = browserInfo;
    
    // IE 不支持
    if (browserInfo.isIE) {
        compatibility.supported = false;
        compatibility.warnings.push('不支持 Internet Explorer');
    }
    
    // 记录兼容性信息
    console.log('浏览器兼容性检查:', compatibility);
    
    return compatibility.supported;
}

/**
 * 初始化用户数据
 */
function initializeUserData() {
    // 使用账号管理器初始化当前账号
    if (typeof accountManager !== 'undefined') {
        accountManager.initializeCurrentAccount();
        console.log('账号管理器初始化完成');
        return;
    }

    // 兜底逻辑：如果账号管理器不可用，使用原有逻辑
    let user = storageManager.getUser();
    
    if (!user) {
        // 创建新用户
        user = new UserProfile({
            name: '学生',
            level: 1,
            totalPoints: 0,
            achievements: [],
            unlockedLevels: ['within-10']
        });
        
        storageManager.setUser(user.toJSON());
        console.log('创建新用户:', user.name);
    } else {
        // 验证现有用户数据
        const userProfile = new UserProfile(user);
        const errors = userProfile.validate();
        
        if (errors.length > 0) {
            console.warn('用户数据验证失败:', errors);
            // 重置用户数据
            const newUser = new UserProfile();
            storageManager.setUser(newUser.toJSON());
        } else {
            // 更新最后活跃时间
            userProfile.lastActiveAt = new Date();
            storageManager.setUser(userProfile.toJSON());
        }
    }
}

/**
 * 显示错误信息
 */
function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #f8d7da;
        color: #721c24;
        padding: 20px;
        border-radius: 10px;
        border: 1px solid #f5c6cb;
        z-index: 9999;
        max-width: 400px;
        text-align: center;
        font-family: Arial, sans-serif;
    `;
    errorDiv.innerHTML = `
        <h3>错误</h3>
        <p>${message}</p>
        <button onclick="this.parentElement.remove()" style="
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
        ">确定</button>
    `;
    document.body.appendChild(errorDiv);
}

/**
 * 全局错误处理
 */
window.addEventListener('error', function(event) {
    console.error('全局错误:', event.error);
    
    // 记录错误到本地存储用于调试
    try {
        const errorLog = storageManager.getItem('mathTraining_errorLog', []);
        errorLog.push({
            message: event.error.message,
            stack: event.error.stack,
            timestamp: new Date().toISOString(),
            url: event.filename,
            line: event.lineno
        });
        
        // 只保留最近50个错误
        if (errorLog.length > 50) {
            errorLog.splice(0, errorLog.length - 50);
        }
        
        storageManager.setItem('mathTraining_errorLog', errorLog);
    } catch (e) {
        console.warn('无法记录错误日志:', e);
    }
    
    showError('发生了一个错误，请刷新页面重试');
});

window.addEventListener('unhandledrejection', function(event) {
    console.error('未处理的Promise拒绝:', event.reason);
    event.preventDefault();
    
    // 记录Promise错误
    try {
        const errorLog = storageManager.getItem('mathTraining_errorLog', []);
        errorLog.push({
            type: 'unhandledrejection',
            reason: event.reason.toString(),
            timestamp: new Date().toISOString()
        });
        storageManager.setItem('mathTraining_errorLog', errorLog);
    } catch (e) {
        console.warn('无法记录Promise错误:', e);
    }
});

/**
 * 性能监控
 */
const performanceMonitor = {
    startTime: performance.now(),
    
    measureLoadTime() {
        const loadTime = performance.now() - this.startTime;
        console.log(`应用加载时间: ${loadTime.toFixed(2)}ms`);
        
        // 记录性能数据
        try {
            const perfData = storageManager.getItem('mathTraining_performance', []);
            perfData.push({
                loadTime: loadTime,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent
            });
            
            // 只保留最近20次记录
            if (perfData.length > 20) {
                perfData.splice(0, perfData.length - 20);
            }
            
            storageManager.setItem('mathTraining_performance', perfData);
        } catch (e) {
            console.warn('无法记录性能数据:', e);
        }
        
        return loadTime;
    },
    
    measureMemoryUsage() {
        if (performance.memory) {
            const memory = {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            };
            console.log('内存使用情况:', memory);
            return memory;
        }
        return null;
    }
};

/**
 * 懒加载和缓存策略
 */
const resourceManager = {
    cache: new Map(),
    
    // 预加载Chart.js
    preloadChartJS() {
        if (typeof Chart === 'undefined') {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
            script.async = true;
            script.onload = () => console.log('Chart.js 预加载完成');
            document.head.appendChild(script);
        }
    },
    
    // 缓存题目数据
    cacheQuestions(level, questions) {
        const cacheKey = `questions_${level}`;
        this.cache.set(cacheKey, {
            data: questions,
            timestamp: Date.now(),
            ttl: 5 * 60 * 1000 // 5分钟TTL
        });
    },
    
    // 获取缓存的题目
    getCachedQuestions(level) {
        const cacheKey = `questions_${level}`;
        const cached = this.cache.get(cacheKey);
        
        if (cached && (Date.now() - cached.timestamp) < cached.ttl) {
            return cached.data;
        }
        
        this.cache.delete(cacheKey);
        return null;
    },
    
    // 清理过期缓存
    cleanupCache() {
        const now = Date.now();
        for (const [key, value] of this.cache.entries()) {
            if (now - value.timestamp > value.ttl) {
                this.cache.delete(key);
            }
        }
    }
};

/**
 * 页面加载完成后初始化应用
 */
document.addEventListener('DOMContentLoaded', function() {
    // 显示加载状态
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'loading-screen';
    loadingDiv.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: white;
        font-family: Arial, sans-serif;
    `;
    loadingDiv.innerHTML = `
        <div style="text-align: center;">
            <div class="loading"></div>
            <h2 style="margin-top: 20px;">数学训练加载中...</h2>
        </div>
    `;
    document.body.appendChild(loadingDiv);
    
    // 预加载资源
    resourceManager.preloadChartJS();
    
    // 延迟初始化以显示加载动画
    setTimeout(() => {
        initializeApp();
        
        // 测量加载时间
        const loadTime = performanceMonitor.measureLoadTime();
        
        // 移除加载屏幕
        setTimeout(() => {
            const loading = document.getElementById('loading-screen');
            if (loading) {
                loading.style.opacity = '0';
                loading.style.transition = 'opacity 0.5s ease-out';
                setTimeout(() => {
                    loading.remove();
                    
                    // 显示性能信息（开发模式）
                    if (loadTime > 3000) {
                        console.warn('应用加载时间较长，建议优化');
                    }
                    
                    // 测量内存使用
                    performanceMonitor.measureMemoryUsage();
                }, 500);
            }
        }, Math.max(1000, Math.min(loadTime, 2000))); // 根据加载时间调整显示时长
    }, 100);
    
    // 定期清理缓存
    setInterval(() => {
        resourceManager.cleanupCache();
    }, 60000); // 每分钟清理一次
});

/**
 * 页面卸载前保存数据
 */
window.addEventListener('beforeunload', function() {
    if (appController) {
        // 保存当前状态
        const user = storageManager.getUser();
        if (user) {
            user.lastActiveAt = new Date();
            storageManager.setUser(user);
        }
        
        // 保存账号数据
        if (typeof accountManager !== 'undefined') {
            accountManager.saveCurrentAccountData();
        }
    }
});

/**
 * 账号切换事件监听
 */
window.addEventListener('accountSwitched', function(event) {
    console.log('账号已切换:', event.detail.accountId);
    
    // 重新初始化相关管理器
    if (typeof wrongQuestionManager !== 'undefined') {
        wrongQuestionManager.loadWrongQuestions();
    }
    
    if (typeof progressTracker !== 'undefined') {
        progressTracker.loadData();
    }
    
    // 更新界面
    if (appController) {
        appController.updateMainMenu();
        if (appController.currentView === 'account-view') {
            appController.updateAccountView();
        }
    }
});

// 导出全局配置供其他模块使用
window.APP_CONFIG = APP_CONFIG;