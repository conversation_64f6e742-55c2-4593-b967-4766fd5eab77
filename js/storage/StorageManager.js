/**
 * 本地存储管理器
 * 处理所有localStorage操作，包括数据序列化、反序列化和错误处理
 */

class StorageManager {
    constructor() {
        this.keys = {
            user: 'mathTraining_user',
            sessions: 'mathTraining_sessions',
            wrongQuestions: 'mathTraining_wrongQuestions',
            settings: 'mathTraining_settings'
        };
        
        this.isSupported = this.checkLocalStorageSupport();
        if (!this.isSupported) {
            console.warn('localStorage不支持，将使用内存存储');
            this.memoryStorage = {};
        }
    }

    /**
     * 检查localStorage支持
     */
    checkLocalStorageSupport() {
        try {
            const test = '__localStorage_test__';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (e) {
            return false;
        }
    }

    /**
     * 存储数据
     */
    setItem(key, value) {
        try {
            const serializedValue = JSON.stringify(value);
            
            if (this.isSupported) {
                localStorage.setItem(key, serializedValue);
            } else {
                this.memoryStorage[key] = serializedValue;
            }
            
            return true;
        } catch (error) {
            console.error('存储数据失败:', error);
            this.handleStorageError(error);
            return false;
        }
    }

    /**
     * 获取数据
     */
    getItem(key, defaultValue = null) {
        try {
            let serializedValue;
            
            if (this.isSupported) {
                serializedValue = localStorage.getItem(key);
            } else {
                serializedValue = this.memoryStorage[key];
            }
            
            if (serializedValue === null || serializedValue === undefined) {
                return defaultValue;
            }
            
            return JSON.parse(serializedValue);
        } catch (error) {
            console.error('获取数据失败:', error);
            this.handleStorageError(error);
            return defaultValue;
        }
    }

    /**
     * 删除数据
     */
    removeItem(key) {
        try {
            if (this.isSupported) {
                localStorage.removeItem(key);
            } else {
                delete this.memoryStorage[key];
            }
            return true;
        } catch (error) {
            console.error('删除数据失败:', error);
            return false;
        }
    }

    /**
     * 清空所有应用数据
     */
    clear() {
        try {
            Object.values(this.keys).forEach(key => {
                this.removeItem(key);
            });
            return true;
        } catch (error) {
            console.error('清空数据失败:', error);
            return false;
        }
    }

    /**
     * 获取存储使用情况
     */
    getStorageInfo() {
        if (!this.isSupported) {
            return {
                used: Object.keys(this.memoryStorage).length,
                total: 'unlimited',
                percentage: 0
            };
        }

        try {
            let used = 0;
            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    used += localStorage[key].length;
                }
            }
            
            // 估算localStorage总容量（通常为5MB）
            const total = 5 * 1024 * 1024;
            const percentage = Math.round((used / total) * 100);
            
            return {
                used: used,
                total: total,
                percentage: percentage
            };
        } catch (error) {
            console.error('获取存储信息失败:', error);
            return { used: 0, total: 0, percentage: 0 };
        }
    }

    /**
     * 处理存储错误
     */
    handleStorageError(error) {
        if (error.name === 'QuotaExceededError') {
            console.warn('存储空间不足，尝试清理旧数据');
            this.cleanupOldData();
        } else if (error.name === 'SecurityError') {
            console.warn('存储访问被拒绝，可能是隐私模式');
        } else {
            console.error('未知存储错误:', error);
        }
    }

    /**
     * 清理旧数据
     */
    cleanupOldData() {
        try {
            // 清理30天前的练习记录
            const sessions = this.getSessions();
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            
            const recentSessions = sessions.filter(session => {
                const sessionDate = new Date(session.startTime);
                return sessionDate > thirtyDaysAgo;
            });
            
            this.setSessions(recentSessions);
            
            // 清理已掌握的错题
            const wrongQuestions = this.getWrongQuestions();
            const activewrongQuestions = wrongQuestions.filter(wq => !wq.masteredAt);
            this.setWrongQuestions(activewrongQuestions);
            
            console.log('旧数据清理完成');
        } catch (error) {
            console.error('清理旧数据失败:', error);
        }
    }

    /**
     * 数据备份
     */
    exportData() {
        try {
            const data = {
                user: this.getUser(),
                sessions: this.getSessions(),
                wrongQuestions: this.getWrongQuestions(),
                settings: this.getSettings(),
                exportTime: new Date().toISOString()
            };
            
            return JSON.stringify(data, null, 2);
        } catch (error) {
            console.error('导出数据失败:', error);
            return null;
        }
    }

    /**
     * 数据恢复
     */
    importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            
            if (data.user) this.setUser(data.user);
            if (data.sessions) this.setSessions(data.sessions);
            if (data.wrongQuestions) this.setWrongQuestions(data.wrongQuestions);
            if (data.settings) this.setSettings(data.settings);
            
            console.log('数据导入成功');
            return true;
        } catch (error) {
            console.error('导入数据失败:', error);
            return false;
        }
    }

    // 用户数据操作
    getUser() {
        return this.getItem(this.keys.user);
    }

    setUser(userData) {
        return this.setItem(this.keys.user, userData);
    }

    // 练习记录操作
    getSessions() {
        return this.getItem(this.keys.sessions, []);
    }

    setSessions(sessions) {
        return this.setItem(this.keys.sessions, sessions);
    }

    addSession(session) {
        const sessions = this.getSessions();
        sessions.push(session);
        return this.setSessions(sessions);
    }

    // 错题数据操作
    getWrongQuestions() {
        return this.getItem(this.keys.wrongQuestions, []);
    }

    setWrongQuestions(wrongQuestions) {
        return this.setItem(this.keys.wrongQuestions, wrongQuestions);
    }

    addWrongQuestion(wrongQuestion) {
        const wrongQuestions = this.getWrongQuestions();
        
        // 检查是否已存在相同题目
        const existingIndex = wrongQuestions.findIndex(wq => 
            wq.question.operand1 === wrongQuestion.question.operand1 &&
            wq.question.operand2 === wrongQuestion.question.operand2 &&
            wq.question.type === wrongQuestion.question.type
        );
        
        if (existingIndex >= 0) {
            // 更新现有错题
            wrongQuestions[existingIndex].wrongCount++;
            wrongQuestions[existingIndex].lastWrongAt = new Date();
            wrongQuestions[existingIndex].correctStreak = 0;
        } else {
            // 添加新错题
            wrongQuestions.push(wrongQuestion);
        }
        
        return this.setWrongQuestions(wrongQuestions);
    }

    updateWrongQuestion(questionId, updates) {
        const wrongQuestions = this.getWrongQuestions();
        const index = wrongQuestions.findIndex(wq => wq.id === questionId);
        
        if (index >= 0) {
            wrongQuestions[index] = { ...wrongQuestions[index], ...updates };
            return this.setWrongQuestions(wrongQuestions);
        }
        
        return false;
    }

    removeWrongQuestion(questionId) {
        const wrongQuestions = this.getWrongQuestions();
        const filteredQuestions = wrongQuestions.filter(wq => wq.id !== questionId);
        return this.setWrongQuestions(filteredQuestions);
    }

    // 设置数据操作
    getSettings() {
        return this.getItem(this.keys.settings, {
            soundEnabled: true,
            animationEnabled: true,
            theme: 'default',
            difficulty: 'normal'
        });
    }

    setSettings(settings) {
        return this.setItem(this.keys.settings, settings);
    }

    updateSettings(updates) {
        const currentSettings = this.getSettings();
        const newSettings = { ...currentSettings, ...updates };
        return this.setSettings(newSettings);
    }
}

// 创建全局实例
const storageManager = new StorageManager();