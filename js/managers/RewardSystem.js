/**
 * 奖励系统
 * 负责计算积分、管理成就和奖励机制
 */

class RewardSystem {
    constructor() {
        this.pointsConfig = {
            basePoints: 10,           // 每答对一题的基础分数
            speedBonus: {
                fast: { threshold: 5, points: 30 },      // ≤5秒额外奖励
                medium: { threshold: 10, points: 20 },   // ≤10秒额外奖励
                slow: { threshold: 15, points: 10 }      // ≤15秒额外奖励
            },
            accuracyBonus: {
                perfect: { threshold: 100, points: 100 }, // 100%正确率
                excellent: { threshold: 90, points: 50 }, // ≥90%正确率
                good: { threshold: 80, points: 30 }       // ≥80%正确率
            },
            streakMultiplier: {
                5: 1.2,   // 连击5次，1.2倍积分
                10: 1.5,  // 连击10次，1.5倍积分
                15: 2.0,  // 连击15次，2倍积分
                20: 2.5   // 连击20次，2.5倍积分
            }
        };

        this.achievements = this.initializeAchievements();
    }

    /**
     * 初始化成就定义
     */
    initializeAchievements() {
        return {
            // 基础成就
            'first-session': {
                id: 'first-session',
                name: '初次尝试',
                description: '完成第一次练习',
                icon: '🌟',
                points: 50,
                condition: { type: 'session_count', value: 1 }
            },
            'perfectionist': {
                id: 'perfectionist',
                name: '完美主义者',
                description: '单次练习100%正确率',
                icon: '💯',
                points: 100,
                condition: { type: 'accuracy', value: 100 }
            },
            'accuracy-80': {
                id: 'accuracy-80',
                name: '准确射手',
                description: '单次练习80%以上正确率',
                icon: '🎯',
                points: 50,
                condition: { type: 'accuracy', value: 80 }
            },

            // 速度成就
            'speed-demon': {
                id: 'speed-demon',
                name: '速度之王',
                description: '平均答题时间≤5秒',
                icon: '⚡',
                points: 80,
                condition: { type: 'avg_time', value: 5 }
            },
            'quick-thinker': {
                id: 'quick-thinker',
                name: '思维敏捷',
                description: '平均答题时间≤10秒',
                icon: '🧠',
                points: 50,
                condition: { type: 'avg_time', value: 10 }
            },

            // 坚持成就
            'persistent': {
                id: 'persistent',
                name: '坚持不懈',
                description: '连续练习7天',
                icon: '💪',
                points: 100,
                condition: { type: 'streak_days', value: 7 }
            },
            'dedicated': {
                id: 'dedicated',
                name: '专注学习',
                description: '连续练习30天',
                icon: '🏆',
                points: 300,
                condition: { type: 'streak_days', value: 30 }
            },

            // 积分成就
            'high-scorer': {
                id: 'high-scorer',
                name: '高分达人',
                description: '总积分达到1000分',
                icon: '💎',
                points: 100,
                condition: { type: 'total_points', value: 1000 }
            },
            'point-master': {
                id: 'point-master',
                name: '积分大师',
                description: '总积分达到5000分',
                icon: '👑',
                points: 200,
                condition: { type: 'total_points', value: 5000 }
            },

            // 练习量成就
            'hundred-questions': {
                id: 'hundred-questions',
                name: '百题达人',
                description: '累计完成100道题目',
                icon: '📚',
                points: 80,
                condition: { type: 'total_questions', value: 100 }
            },
            'thousand-questions': {
                id: 'thousand-questions',
                name: '千题挑战者',
                description: '累计完成1000道题目',
                icon: '🎓',
                points: 200,
                condition: { type: 'total_questions', value: 1000 }
            },

            // 连击成就
            'combo-master': {
                id: 'combo-master',
                name: '连击高手',
                description: '单次练习连击20次',
                icon: '🔥',
                points: 100,
                condition: { type: 'max_streak', value: 20 }
            },
            'streak-legend': {
                id: 'streak-legend',
                name: '连击传说',
                description: '单次练习连击50次',
                icon: '🌟',
                points: 200,
                condition: { type: 'max_streak', value: 50 }
            },

            // 级别成就
            'level-master-10': {
                id: 'level-master-10',
                name: '10以内大师',
                description: '10以内加减法平均正确率≥95%',
                icon: '🥉',
                points: 60,
                condition: { type: 'level_mastery', level: 'within-10', accuracy: 95 }
            },
            'level-master-20': {
                id: 'level-master-20',
                name: '20以内大师',
                description: '20以内加减法平均正确率≥95%',
                icon: '🥈',
                points: 80,
                condition: { type: 'level_mastery', level: 'within-20', accuracy: 95 }
            },
            'level-master-100': {
                id: 'level-master-100',
                name: '100以内大师',
                description: '100以内加减法平均正确率≥95%',
                icon: '🥇',
                points: 100,
                condition: { type: 'level_mastery', level: 'within-100', accuracy: 95 }
            },
            'multiplication-master': {
                id: 'multiplication-master',
                name: '乘法大师',
                description: '乘法表平均正确率≥95%',
                icon: '🏅',
                points: 120,
                condition: { type: 'level_mastery', level: 'multiplication', accuracy: 95 }
            },

            // 错题成就
            'error-conqueror': {
                id: 'error-conqueror',
                name: '错题征服者',
                description: '掌握50道错题',
                icon: '⚔️',
                points: 100,
                condition: { type: 'mastered_wrong_questions', value: 50 }
            }
        };
    }

    /**
     * 计算练习积分
     */
    calculatePoints(sessionData) {
        const { correctCount, totalCount, answers, startTime, endTime } = sessionData;
        
        // 基础积分
        let totalPoints = correctCount * this.pointsConfig.basePoints;
        
        // 速度奖励
        const speedBonus = this.calculateSpeedBonus(answers, totalCount, startTime, endTime);
        totalPoints += speedBonus;
        
        // 正确率奖励
        const accuracy = Math.round((correctCount / totalCount) * 100);
        const accuracyBonus = this.calculateAccuracyBonus(accuracy);
        totalPoints += accuracyBonus;
        
        // 连击奖励
        const maxStreak = this.calculateMaxStreak(answers);
        const streakBonus = this.calculateStreakBonus(totalPoints, maxStreak);
        totalPoints += streakBonus;

        return {
            totalPoints: Math.round(totalPoints),
            breakdown: {
                basePoints: correctCount * this.pointsConfig.basePoints,
                speedBonus,
                accuracyBonus,
                streakBonus,
                maxStreak
            }
        };
    }

    /**
     * 计算速度奖励
     */
    calculateSpeedBonus(answers, totalCount, startTime, endTime) {
        if (!answers || answers.length === 0) return 0;
        
        // 计算平均答题时间
        const totalTime = (new Date(endTime) - new Date(startTime)) / 1000;
        const avgTime = totalTime / totalCount;
        
        const { speedBonus } = this.pointsConfig;
        
        if (avgTime <= speedBonus.fast.threshold) {
            return speedBonus.fast.points;
        } else if (avgTime <= speedBonus.medium.threshold) {
            return speedBonus.medium.points;
        } else if (avgTime <= speedBonus.slow.threshold) {
            return speedBonus.slow.points;
        }
        
        return 0;
    }

    /**
     * 计算正确率奖励
     */
    calculateAccuracyBonus(accuracy) {
        const { accuracyBonus } = this.pointsConfig;
        
        if (accuracy >= accuracyBonus.perfect.threshold) {
            return accuracyBonus.perfect.points;
        } else if (accuracy >= accuracyBonus.excellent.threshold) {
            return accuracyBonus.excellent.points;
        } else if (accuracy >= accuracyBonus.good.threshold) {
            return accuracyBonus.good.points;
        }
        
        return 0;
    }

    /**
     * 计算最大连击数
     */
    calculateMaxStreak(answers) {
        if (!answers || answers.length === 0) return 0;
        
        let maxStreak = 0;
        let currentStreak = 0;
        
        answers.forEach(answer => {
            if (answer.isCorrect) {
                currentStreak++;
                maxStreak = Math.max(maxStreak, currentStreak);
            } else {
                currentStreak = 0;
            }
        });
        
        return maxStreak;
    }

    /**
     * 计算连击奖励
     */
    calculateStreakBonus(basePoints, maxStreak) {
        const { streakMultiplier } = this.pointsConfig;
        
        // 找到适用的连击倍数
        let multiplier = 1;
        Object.keys(streakMultiplier)
            .map(Number)
            .sort((a, b) => b - a) // 从大到小排序
            .forEach(threshold => {
                if (maxStreak >= threshold && multiplier === 1) {
                    multiplier = streakMultiplier[threshold];
                }
            });
        
        return Math.round(basePoints * (multiplier - 1));
    }

    /**
     * 检查成就
     */
    checkAchievements(sessionData, userData) {
        const newAchievements = [];
        const userAchievements = userData.achievements || [];
        
        Object.values(this.achievements).forEach(achievement => {
            if (!userAchievements.includes(achievement.id)) {
                if (this.checkAchievementCondition(achievement.condition, sessionData, userData)) {
                    newAchievements.push(achievement);
                }
            }
        });
        
        return newAchievements;
    }

    /**
     * 检查成就条件
     */
    checkAchievementCondition(condition, sessionData, userData) {
        switch (condition.type) {
            case 'session_count':
                const sessions = storageManager.getSessions();
                return sessions.length >= condition.value;
                
            case 'accuracy':
                const accuracy = Math.round((sessionData.correctCount / sessionData.totalCount) * 100);
                return accuracy >= condition.value;
                
            case 'avg_time':
                const totalTime = (new Date(sessionData.endTime) - new Date(sessionData.startTime)) / 1000;
                const avgTime = totalTime / sessionData.totalCount;
                return avgTime <= condition.value;
                
            case 'streak_days':
                return progressTracker.calculateStreakDays() >= condition.value;
                
            case 'total_points':
                return userData.totalPoints >= condition.value;
                
            case 'total_questions':
                const allSessions = storageManager.getSessions();
                const totalQuestions = allSessions.reduce((sum, s) => sum + s.totalCount, 0);
                return totalQuestions >= condition.value;
                
            case 'max_streak':
                const maxStreak = this.calculateMaxStreak(sessionData.answers);
                return maxStreak >= condition.value;
                
            case 'level_mastery':
                return this.checkLevelMastery(condition.level, condition.accuracy);
                
            case 'mastered_wrong_questions':
                const wrongQuestions = storageManager.getWrongQuestions();
                const masteredCount = wrongQuestions.filter(wq => wq.masteredAt).length;
                return masteredCount >= condition.value;
                
            default:
                return false;
        }
    }

    /**
     * 检查级别掌握情况
     */
    checkLevelMastery(level, requiredAccuracy) {
        const sessions = storageManager.getSessions();
        const levelSessions = sessions.filter(s => s.level === level);
        
        if (levelSessions.length < 3) return false; // 至少需要3次练习
        
        const totalQuestions = levelSessions.reduce((sum, s) => sum + s.totalCount, 0);
        const totalCorrect = levelSessions.reduce((sum, s) => sum + s.correctCount, 0);
        const accuracy = (totalCorrect / totalQuestions) * 100;
        
        return accuracy >= requiredAccuracy;
    }

    /**
     * 解锁奖励
     */
    unlockRewards(points, userData) {
        const rewards = [];
        
        // 基于积分的奖励
        const pointThresholds = [100, 500, 1000, 2000, 5000, 10000];
        pointThresholds.forEach(threshold => {
            if (userData.totalPoints >= threshold) {
                rewards.push({
                    type: 'avatar',
                    id: `avatar_${threshold}`,
                    name: `${threshold}分头像`,
                    description: `达到${threshold}积分解锁`,
                    unlocked: true
                });
            }
        });
        
        // 基于成就的奖励
        const achievementCount = userData.achievements ? userData.achievements.length : 0;
        const achievementThresholds = [5, 10, 15, 20];
        achievementThresholds.forEach(threshold => {
            if (achievementCount >= threshold) {
                rewards.push({
                    type: 'title',
                    id: `title_${threshold}`,
                    name: `${threshold}成就称号`,
                    description: `获得${threshold}个成就解锁`,
                    unlocked: true
                });
            }
        });
        
        return rewards;
    }

    /**
     * 显示庆祝效果
     */
    displayCelebration(type = 'default', data = {}) {
        const celebrations = {
            'perfect_score': {
                message: '完美表现！🎉',
                animation: 'bounce',
                duration: 3000,
                sound: 'success'
            },
            'new_achievement': {
                message: `🏆 解锁成就：${data.achievementName}`,
                animation: 'slideIn',
                duration: 4000,
                sound: 'achievement'
            },
            'level_unlock': {
                message: `🔓 解锁新级别：${data.levelName}`,
                animation: 'fadeIn',
                duration: 3000,
                sound: 'unlock'
            },
            'high_streak': {
                message: `🔥 连击 ${data.streak} 次！`,
                animation: 'pulse',
                duration: 2000,
                sound: 'streak'
            },
            'default': {
                message: '太棒了！',
                animation: 'bounce',
                duration: 2000,
                sound: 'default'
            }
        };
        
        const celebration = celebrations[type] || celebrations['default'];
        
        // 创建庆祝元素
        const celebrationElement = document.createElement('div');
        celebrationElement.className = `celebration celebration-${celebration.animation}`;
        celebrationElement.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: white;
            padding: 20px 40px;
            border-radius: 20px;
            font-size: 1.5rem;
            font-weight: bold;
            text-align: center;
            z-index: 10000;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            animation: ${celebration.animation} 0.6s ease-out;
        `;
        celebrationElement.textContent = celebration.message;
        
        document.body.appendChild(celebrationElement);
        
        // 自动移除
        setTimeout(() => {
            celebrationElement.style.opacity = '0';
            celebrationElement.style.transition = 'opacity 0.5s ease-out';
            setTimeout(() => {
                if (celebrationElement.parentNode) {
                    celebrationElement.parentNode.removeChild(celebrationElement);
                }
            }, 500);
        }, celebration.duration);
        
        // 播放音效（如果支持）
        this.playSound(celebration.sound);
        
        return celebrationElement;
    }

    /**
     * 播放音效
     */
    playSound(soundType) {
        // 这里可以添加音效播放逻辑
        // 由于是纯前端应用，可以使用Web Audio API或预加载的音频文件
        console.log(`播放音效: ${soundType}`);
    }

    /**
     * 获取用户等级
     */
    getUserLevel(totalPoints) {
        const levels = [
            { level: 1, name: '新手', minPoints: 0, maxPoints: 99 },
            { level: 2, name: '学徒', minPoints: 100, maxPoints: 299 },
            { level: 3, name: '熟练', minPoints: 300, maxPoints: 599 },
            { level: 4, name: '专家', minPoints: 600, maxPoints: 999 },
            { level: 5, name: '大师', minPoints: 1000, maxPoints: 1999 },
            { level: 6, name: '宗师', minPoints: 2000, maxPoints: 4999 },
            { level: 7, name: '传奇', minPoints: 5000, maxPoints: 9999 },
            { level: 8, name: '神话', minPoints: 10000, maxPoints: Infinity }
        ];
        
        for (const levelInfo of levels) {
            if (totalPoints >= levelInfo.minPoints && totalPoints <= levelInfo.maxPoints) {
                return levelInfo;
            }
        }
        
        return levels[0]; // 默认返回新手级别
    }

    /**
     * 获取下一级别进度
     */
    getNextLevelProgress(totalPoints) {
        const currentLevel = this.getUserLevel(totalPoints);
        const nextLevelMinPoints = currentLevel.maxPoints + 1;
        
        if (currentLevel.maxPoints === Infinity) {
            return { progress: 100, pointsNeeded: 0 };
        }
        
        const pointsInCurrentLevel = totalPoints - currentLevel.minPoints;
        const pointsNeededForCurrentLevel = currentLevel.maxPoints - currentLevel.minPoints + 1;
        const progress = Math.round((pointsInCurrentLevel / pointsNeededForCurrentLevel) * 100);
        const pointsNeeded = nextLevelMinPoints - totalPoints;
        
        return { progress, pointsNeeded };
    }

    /**
     * 获取奖励统计
     */
    getRewardStatistics(userData) {
        const totalAchievements = Object.keys(this.achievements).length;
        const unlockedAchievements = userData.achievements ? userData.achievements.length : 0;
        const achievementProgress = Math.round((unlockedAchievements / totalAchievements) * 100);
        
        const currentLevel = this.getUserLevel(userData.totalPoints);
        const nextLevelProgress = this.getNextLevelProgress(userData.totalPoints);
        
        return {
            totalPoints: userData.totalPoints,
            currentLevel: currentLevel,
            nextLevelProgress: nextLevelProgress,
            achievements: {
                total: totalAchievements,
                unlocked: unlockedAchievements,
                progress: achievementProgress
            },
            rewards: this.unlockRewards(userData.totalPoints, userData)
        };
    }

    /**
     * 获取成就详情
     */
    getAchievementDetails(achievementId) {
        return this.achievements[achievementId] || null;
    }

    /**
     * 获取所有成就
     */
    getAllAchievements() {
        return Object.values(this.achievements);
    }
}

// 创建全局实例
const rewardSystem = new RewardSystem();