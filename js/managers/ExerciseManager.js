/**
 * 练习管理器
 * 负责管理练习会话、题目顺序和答案检查
 */

class ExerciseManager {
    constructor() {
        this.currentSession = null;
        this.currentQuestionIndex = 0;
        this.startTime = null;
        this.questionStartTime = null;
        this.isActive = false;
    }

    /**
     * 开始新的练习会话
     */
    startSession(level, questionCount = 50) {
        try {
            // 生成题目
            const questions = questionBankManager.generateQuestions(level, questionCount);
            
            // 创建新会话
            const user = storageManager.getUser();
            this.currentSession = new ExerciseSession({
                userId: user ? user.id : 'anonymous',
                level: level,
                questionBank: 'default',
                questions: questions.map(q => q.toJSON()),
                startTime: new Date()
            });

            this.currentQuestionIndex = 0;
            this.startTime = new Date();
            this.isActive = true;
            this.questionStartTime = new Date();

            console.log(`开始练习会话: ${level}, ${questionCount}道题`);
            return this.currentSession;
        } catch (error) {
            console.error('开始练习会话失败:', error);
            throw error;
        }
    }

    /**
     * 开始错题复训会话
     */
    startReviewSession(wrongQuestions, questionCount = 20) {
        try {
            // 生成复习题目
            const reviewQuestions = questionBankManager.generateReviewQuestions(wrongQuestions, questionCount);
            
            const user = storageManager.getUser();
            this.currentSession = new ExerciseSession({
                userId: user ? user.id : 'anonymous',
                level: 'review',
                questionBank: 'wrong-questions',
                questions: reviewQuestions.map(q => q.toJSON()),
                startTime: new Date()
            });

            this.currentQuestionIndex = 0;
            this.startTime = new Date();
            this.isActive = true;
            this.questionStartTime = new Date();

            console.log(`开始错题复训: ${questionCount}道题`);
            return this.currentSession;
        } catch (error) {
            console.error('开始错题复训失败:', error);
            throw error;
        }
    }

    /**
     * 获取当前题目
     */
    getCurrentQuestion() {
        if (!this.isActive || !this.currentSession) {
            return null;
        }

        if (this.currentQuestionIndex >= this.currentSession.questions.length) {
            return null;
        }

        const questionData = this.currentSession.questions[this.currentQuestionIndex];
        return new Question(questionData);
    }

    /**
     * 获取练习进度
     */
    getProgress() {
        if (!this.currentSession) {
            return { current: 0, total: 0, percentage: 0 };
        }

        const current = this.currentQuestionIndex + 1;
        const total = this.currentSession.questions.length;
        const percentage = Math.round((current / total) * 100);

        return { current, total, percentage };
    }

    /**
     * 检查答案
     */
    checkAnswer(userAnswer) {
        if (!this.isActive || !this.currentSession) {
            throw new Error('没有活跃的练习会话');
        }

        const currentQuestion = this.getCurrentQuestion();
        if (!currentQuestion) {
            throw new Error('没有当前题目');
        }

        // 计算答题时间
        const timeSpent = this.questionStartTime ? 
            Math.floor((new Date() - this.questionStartTime) / 1000) : 0;

        // 检查答案是否正确
        const isCorrect = this.validateAnswer(userAnswer, currentQuestion.correctAnswer);

        // 记录答案
        this.currentSession.addAnswer(
            currentQuestion.id,
            userAnswer,
            isCorrect,
            timeSpent
        );

        // 处理错题
        if (!isCorrect) {
            this.handleWrongAnswer(currentQuestion, userAnswer);
        } else {
            this.handleCorrectAnswer(currentQuestion);
        }

        // 准备下一题
        this.moveToNextQuestion();

        return {
            isCorrect,
            correctAnswer: currentQuestion.correctAnswer,
            timeSpent,
            isLastQuestion: this.currentQuestionIndex >= this.currentSession.questions.length
        };
    }

    /**
     * 验证答案
     */
    validateAnswer(userAnswer, correctAnswer) {
        // 处理数字输入
        const userNum = parseFloat(userAnswer);
        const correctNum = parseFloat(correctAnswer);

        if (isNaN(userNum) || isNaN(correctNum)) {
            return false;
        }

        return Math.abs(userNum - correctNum) < 0.001; // 处理浮点数精度问题
    }

    /**
     * 处理错误答案
     */
    handleWrongAnswer(question, userAnswer) {
        // 创建错题记录
        const wrongQuestion = new WrongQuestion({
            question: question,
            userAnswer: userAnswer,
            correctAnswer: question.correctAnswer
        });

        // 添加到错题本
        storageManager.addWrongQuestion(wrongQuestion.toJSON());
    }

    /**
     * 处理正确答案
     */
    handleCorrectAnswer(question) {
        // 检查是否是错题复训中的题目
        if (this.currentSession.questionBank === 'wrong-questions') {
            const wrongQuestions = storageManager.getWrongQuestions();
            const matchingWrongQ = wrongQuestions.find(wq => 
                wq.question.operand1 === question.operand1 &&
                wq.question.operand2 === question.operand2 &&
                wq.question.type === question.type
            );

            if (matchingWrongQ) {
                // 标记为正确
                matchingWrongQ.correctStreak = (matchingWrongQ.correctStreak || 0) + 1;
                
                // 如果连续3次正确，标记为已掌握
                if (matchingWrongQ.correctStreak >= 3) {
                    matchingWrongQ.masteredAt = new Date();
                }

                storageManager.updateWrongQuestion(matchingWrongQ.id, matchingWrongQ);
            }
        }
    }

    /**
     * 移动到下一题
     */
    moveToNextQuestion() {
        this.currentQuestionIndex++;
        this.questionStartTime = new Date();

        // 检查是否完成所有题目
        if (this.currentQuestionIndex >= this.currentSession.questions.length) {
            this.completeSession();
        }
    }

    /**
     * 跳过当前题目
     */
    skipQuestion() {
        if (!this.isActive || !this.currentSession) {
            throw new Error('没有活跃的练习会话');
        }

        const currentQuestion = this.getCurrentQuestion();
        if (!currentQuestion) {
            return;
        }

        // 记录为错误答案
        this.currentSession.addAnswer(
            currentQuestion.id,
            null,
            false,
            0
        );

        // 添加到错题本
        this.handleWrongAnswer(currentQuestion, null);

        // 移动到下一题
        this.moveToNextQuestion();

        return {
            isCorrect: false,
            correctAnswer: currentQuestion.correctAnswer,
            timeSpent: 0,
            isLastQuestion: this.currentQuestionIndex >= this.currentSession.questions.length
        };
    }

    /**
     * 完成练习会话
     */
    completeSession() {
        if (!this.currentSession) {
            return null;
        }

        // 完成会话
        this.currentSession.complete();
        
        // 保存会话记录
        storageManager.addSession(this.currentSession.toJSON());

        // 更新用户数据
        this.updateUserProgress();

        // 检查级别解锁
        this.checkLevelUnlock();

        const sessionResult = {
            accuracy: this.currentSession.calculateAccuracy(),
            totalTime: this.currentSession.getTotalTimeSpent(),
            score: this.currentSession.score,
            correctCount: this.currentSession.correctCount,
            totalCount: this.currentSession.totalCount,
            level: this.currentSession.level
        };

        // 重置状态
        this.isActive = false;
        this.currentQuestionIndex = 0;
        this.startTime = null;
        this.questionStartTime = null;

        console.log('练习会话完成:', sessionResult);
        return sessionResult;
    }

    /**
     * 更新用户进度
     */
    updateUserProgress() {
        const user = storageManager.getUser();
        if (!user) return;

        const userProfile = new UserProfile(user);
        
        // 增加积分
        userProfile.totalPoints += this.currentSession.score;
        
        // 更新最后活跃时间
        userProfile.lastActiveAt = new Date();

        storageManager.setUser(userProfile.toJSON());
    }

    /**
     * 检查级别解锁
     */
    checkLevelUnlock() {
        const accuracy = this.currentSession.calculateAccuracy();
        if (accuracy < 80) return; // 需要80%以上正确率才能解锁

        const user = storageManager.getUser();
        if (!user) return;

        const userProfile = new UserProfile(user);
        const currentLevel = this.currentSession.level;

        // 定义级别解锁顺序
        const levelOrder = ['within-10', 'within-20', 'within-100', 'within-1000', 'multiplication', 'division'];
        const currentIndex = levelOrder.indexOf(currentLevel);
        
        if (currentIndex >= 0 && currentIndex < levelOrder.length - 1) {
            const nextLevel = levelOrder[currentIndex + 1];
            
            if (!userProfile.unlockedLevels.includes(nextLevel)) {
                userProfile.unlockedLevels.push(nextLevel);
                storageManager.setUser(userProfile.toJSON());
                
                console.log(`解锁新级别: ${nextLevel}`);
                
                // 触发级别解锁事件
                this.triggerLevelUnlockEvent(nextLevel);
            }
        }
    }

    /**
     * 触发级别解锁事件
     */
    triggerLevelUnlockEvent(level) {
        const event = new CustomEvent('levelUnlocked', {
            detail: { level }
        });
        window.dispatchEvent(event);
    }

    /**
     * 暂停练习
     */
    pauseSession() {
        if (this.isActive) {
            this.isActive = false;
            console.log('练习已暂停');
        }
    }

    /**
     * 恢复练习
     */
    resumeSession() {
        if (this.currentSession && !this.isActive) {
            this.isActive = true;
            this.questionStartTime = new Date();
            console.log('练习已恢复');
        }
    }

    /**
     * 结束当前会话
     */
    endSession() {
        if (this.currentSession) {
            this.completeSession();
        }
    }

    /**
     * 获取会话统计
     */
    getSessionStats() {
        if (!this.currentSession) {
            return null;
        }

        return {
            level: this.currentSession.level,
            progress: this.getProgress(),
            accuracy: this.currentSession.calculateAccuracy(),
            correctCount: this.currentSession.correctCount,
            totalCount: this.currentSession.totalCount,
            timeSpent: this.startTime ? Math.floor((new Date() - this.startTime) / 1000) : 0,
            isActive: this.isActive
        };
    }

    /**
     * 获取答题历史
     */
    getAnswerHistory() {
        if (!this.currentSession) {
            return [];
        }

        return this.currentSession.answers.map((answer, index) => {
            const question = new Question(this.currentSession.questions[index]);
            return {
                question: question.getExpression(),
                userAnswer: answer.userAnswer,
                correctAnswer: question.correctAnswer,
                isCorrect: answer.isCorrect,
                timeSpent: answer.timeSpent
            };
        });
    }

    /**
     * 计算当前连击数
     */
    getCurrentStreak() {
        if (!this.currentSession || this.currentSession.answers.length === 0) {
            return 0;
        }

        let streak = 0;
        for (let i = this.currentSession.answers.length - 1; i >= 0; i--) {
            if (this.currentSession.answers[i].isCorrect) {
                streak++;
            } else {
                break;
            }
        }

        return streak;
    }

    /**
     * 获取题目提示
     */
    getHint() {
        const currentQuestion = this.getCurrentQuestion();
        if (!currentQuestion) return null;

        const { type, operand1, operand2 } = currentQuestion;
        
        switch (type) {
            case 'addition':
                return `提示：${operand1} + ${operand2}，可以先数到${operand1}，再往后数${operand2}个数`;
            case 'subtraction':
                return `提示：${operand1} - ${operand2}，可以从${operand1}开始往前数${operand2}个数`;
            case 'multiplication':
                return `提示：${operand1} × ${operand2}，就是${operand1}个${operand2}相加`;
            default:
                return '仔细思考一下吧！';
        }
    }
}

// 创建全局实例
const exerciseManager = new ExerciseManager();