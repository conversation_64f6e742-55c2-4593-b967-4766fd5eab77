/**
 * 进度跟踪器
 * 负责记录和分析学习进度数据
 */

class ProgressTracker {
    constructor() {
        this.sessions = [];
        this.achievements = [];
        this.loadData();
    }

    /**
     * 加载数据
     */
    loadData() {
        this.sessions = storageManager.getSessions();
        const user = storageManager.getUser();
        this.achievements = user ? user.achievements || [] : [];
    }

    /**
     * 记录练习会话
     */
    recordSession(sessionData) {
        try {
            // 验证会话数据
            if (!this.validateSessionData(sessionData)) {
                throw new Error('无效的会话数据');
            }

            // 添加时间戳
            sessionData.recordedAt = new Date();
            
            // 保存会话
            storageManager.addSession(sessionData);
            this.sessions.push(sessionData);

            // 更新统计数据
            this.updateStatistics(sessionData);

            console.log('会话记录成功:', sessionData.id);
            return true;
        } catch (error) {
            console.error('记录会话失败:', error);
            return false;
        }
    }

    /**
     * 验证会话数据
     */
    validateSessionData(sessionData) {
        const requiredFields = ['id', 'userId', 'level', 'startTime', 'endTime', 'score', 'correctCount', 'totalCount'];
        
        for (const field of requiredFields) {
            if (sessionData[field] === undefined || sessionData[field] === null) {
                console.error(`缺少必需字段: ${field}`);
                return false;
            }
        }

        // 验证数值合理性
        if (sessionData.correctCount > sessionData.totalCount) {
            console.error('正确数不能大于总数');
            return false;
        }

        if (sessionData.score < 0) {
            console.error('分数不能为负数');
            return false;
        }

        return true;
    }

    /**
     * 更新统计数据
     */
    updateStatistics(sessionData) {
        const user = storageManager.getUser();
        if (!user) return;

        const userProfile = new UserProfile(user);
        
        // 更新总积分
        userProfile.totalPoints += sessionData.score;
        
        // 更新最后活跃时间
        userProfile.lastActiveAt = new Date();

        // 检查成就
        this.checkAchievements(sessionData, userProfile);

        // 保存用户数据
        storageManager.setUser(userProfile.toJSON());
    }

    /**
     * 检查成就
     */
    checkAchievements(sessionData, userProfile) {
        const newAchievements = [];

        // 首次完成成就
        if (this.sessions.length === 1 && !userProfile.achievements.includes('first-session')) {
            newAchievements.push('first-session');
        }

        // 准确率成就
        const accuracy = Math.round((sessionData.correctCount / sessionData.totalCount) * 100);
        if (accuracy === 100 && !userProfile.achievements.includes('perfectionist')) {
            newAchievements.push('perfectionist');
        }
        if (accuracy >= 80 && !userProfile.achievements.includes('accuracy-80')) {
            newAchievements.push('accuracy-80');
        }

        // 速度成就
        const avgTime = (sessionData.endTime - sessionData.startTime) / (1000 * sessionData.totalCount);
        if (avgTime <= 5 && !userProfile.achievements.includes('speed-demon')) {
            newAchievements.push('speed-demon');
        }

        // 坚持成就
        const streakDays = this.calculateStreakDays();
        if (streakDays >= 7 && !userProfile.achievements.includes('persistent')) {
            newAchievements.push('persistent');
        }

        // 积分成就
        if (userProfile.totalPoints >= 1000 && !userProfile.achievements.includes('high-scorer')) {
            newAchievements.push('high-scorer');
        }

        // 添加新成就
        if (newAchievements.length > 0) {
            userProfile.achievements.push(...newAchievements);
            this.achievements = userProfile.achievements;
            
            // 触发成就事件
            newAchievements.forEach(achievement => {
                this.triggerAchievementEvent(achievement);
            });
        }
    }

    /**
     * 触发成就事件
     */
    triggerAchievementEvent(achievementId) {
        const event = new CustomEvent('achievementUnlocked', {
            detail: { achievement: achievementId }
        });
        window.dispatchEvent(event);
    }

    /**
     * 获取进度数据
     */
    getProgressData() {
        return {
            totalSessions: this.sessions.length,
            totalQuestions: this.getTotalQuestions(),
            totalCorrect: this.getTotalCorrect(),
            overallAccuracy: this.getOverallAccuracy(),
            totalTime: this.getTotalTime(),
            averageScore: this.getAverageScore(),
            streakDays: this.calculateStreakDays(),
            levelProgress: this.getLevelProgress(),
            recentSessions: this.getRecentSessions(7),
            achievements: this.achievements
        };
    }

    /**
     * 获取总题目数
     */
    getTotalQuestions() {
        return this.sessions.reduce((total, session) => total + session.totalCount, 0);
    }

    /**
     * 获取总正确数
     */
    getTotalCorrect() {
        return this.sessions.reduce((total, session) => total + session.correctCount, 0);
    }

    /**
     * 获取总体正确率
     */
    getOverallAccuracy() {
        const totalQuestions = this.getTotalQuestions();
        const totalCorrect = this.getTotalCorrect();
        return totalQuestions > 0 ? Math.round((totalCorrect / totalQuestions) * 100) : 0;
    }

    /**
     * 获取总用时
     */
    getTotalTime() {
        return this.sessions.reduce((total, session) => {
            const sessionTime = new Date(session.endTime) - new Date(session.startTime);
            return total + Math.floor(sessionTime / 1000);
        }, 0);
    }

    /**
     * 获取平均分数
     */
    getAverageScore() {
        if (this.sessions.length === 0) return 0;
        const totalScore = this.sessions.reduce((total, session) => total + session.score, 0);
        return Math.round(totalScore / this.sessions.length);
    }

    /**
     * 计算连续练习天数
     */
    calculateStreakDays() {
        if (this.sessions.length === 0) return 0;

        // 按日期分组会话
        const sessionsByDate = this.groupSessionsByDate();
        const dates = Object.keys(sessionsByDate).sort().reverse();

        let streakDays = 0;
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        for (let i = 0; i < dates.length; i++) {
            const date = new Date(dates[i]);
            const expectedDate = new Date(today);
            expectedDate.setDate(expectedDate.getDate() - i);

            if (date.getTime() === expectedDate.getTime()) {
                streakDays++;
            } else {
                break;
            }
        }

        return streakDays;
    }

    /**
     * 按日期分组会话
     */
    groupSessionsByDate() {
        const grouped = {};
        
        this.sessions.forEach(session => {
            const date = new Date(session.startTime);
            date.setHours(0, 0, 0, 0);
            const dateKey = date.toISOString().split('T')[0];
            
            if (!grouped[dateKey]) {
                grouped[dateKey] = [];
            }
            grouped[dateKey].push(session);
        });

        return grouped;
    }

    /**
     * 获取级别进度
     */
    getLevelProgress() {
        const levels = ['within-10', 'within-20', 'within-100', 'multiplication'];
        const progress = {};

        levels.forEach(level => {
            const levelSessions = this.sessions.filter(session => session.level === level);
            
            if (levelSessions.length > 0) {
                const totalQuestions = levelSessions.reduce((sum, s) => sum + s.totalCount, 0);
                const totalCorrect = levelSessions.reduce((sum, s) => sum + s.correctCount, 0);
                const accuracy = Math.round((totalCorrect / totalQuestions) * 100);
                const bestScore = Math.max(...levelSessions.map(s => s.score));
                const averageTime = levelSessions.reduce((sum, s) => {
                    return sum + (new Date(s.endTime) - new Date(s.startTime));
                }, 0) / levelSessions.length / 1000;

                progress[level] = {
                    sessions: levelSessions.length,
                    totalQuestions,
                    totalCorrect,
                    accuracy,
                    bestScore,
                    averageTime: Math.round(averageTime)
                };
            } else {
                progress[level] = {
                    sessions: 0,
                    totalQuestions: 0,
                    totalCorrect: 0,
                    accuracy: 0,
                    bestScore: 0,
                    averageTime: 0
                };
            }
        });

        return progress;
    }

    /**
     * 获取最近会话
     */
    getRecentSessions(days = 7) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);

        return this.sessions
            .filter(session => new Date(session.startTime) >= cutoffDate)
            .sort((a, b) => new Date(b.startTime) - new Date(a.startTime));
    }

    /**
     * 计算统计数据
     */
    calculateStatistics() {
        const stats = {
            daily: this.getDailyStats(),
            weekly: this.getWeeklyStats(),
            monthly: this.getMonthlyStats(),
            trends: this.getTrends()
        };

        return stats;
    }

    /**
     * 获取每日统计
     */
    getDailyStats(days = 30) {
        const stats = [];
        const today = new Date();

        for (let i = days - 1; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            date.setHours(0, 0, 0, 0);

            const nextDate = new Date(date);
            nextDate.setDate(nextDate.getDate() + 1);

            const daySessions = this.sessions.filter(session => {
                const sessionDate = new Date(session.startTime);
                return sessionDate >= date && sessionDate < nextDate;
            });

            const totalQuestions = daySessions.reduce((sum, s) => sum + s.totalCount, 0);
            const totalCorrect = daySessions.reduce((sum, s) => sum + s.correctCount, 0);
            const accuracy = totalQuestions > 0 ? Math.round((totalCorrect / totalQuestions) * 100) : 0;
            const totalScore = daySessions.reduce((sum, s) => sum + s.score, 0);

            stats.push({
                date: date.toISOString().split('T')[0],
                sessions: daySessions.length,
                questions: totalQuestions,
                correct: totalCorrect,
                accuracy: accuracy,
                score: totalScore
            });
        }

        return stats;
    }

    /**
     * 获取每周统计
     */
    getWeeklyStats(weeks = 12) {
        const stats = [];
        const today = new Date();

        for (let i = weeks - 1; i >= 0; i--) {
            const weekStart = new Date(today);
            weekStart.setDate(weekStart.getDate() - (weekStart.getDay() + 7 * i));
            weekStart.setHours(0, 0, 0, 0);

            const weekEnd = new Date(weekStart);
            weekEnd.setDate(weekEnd.getDate() + 7);

            const weekSessions = this.sessions.filter(session => {
                const sessionDate = new Date(session.startTime);
                return sessionDate >= weekStart && sessionDate < weekEnd;
            });

            const totalQuestions = weekSessions.reduce((sum, s) => sum + s.totalCount, 0);
            const totalCorrect = weekSessions.reduce((sum, s) => sum + s.correctCount, 0);
            const accuracy = totalQuestions > 0 ? Math.round((totalCorrect / totalQuestions) * 100) : 0;
            const totalScore = weekSessions.reduce((sum, s) => sum + s.score, 0);

            stats.push({
                weekStart: weekStart.toISOString().split('T')[0],
                sessions: weekSessions.length,
                questions: totalQuestions,
                correct: totalCorrect,
                accuracy: accuracy,
                score: totalScore
            });
        }

        return stats;
    }

    /**
     * 获取每月统计
     */
    getMonthlyStats(months = 6) {
        const stats = [];
        const today = new Date();

        for (let i = months - 1; i >= 0; i--) {
            const monthStart = new Date(today.getFullYear(), today.getMonth() - i, 1);
            const monthEnd = new Date(today.getFullYear(), today.getMonth() - i + 1, 1);

            const monthSessions = this.sessions.filter(session => {
                const sessionDate = new Date(session.startTime);
                return sessionDate >= monthStart && sessionDate < monthEnd;
            });

            const totalQuestions = monthSessions.reduce((sum, s) => sum + s.totalCount, 0);
            const totalCorrect = monthSessions.reduce((sum, s) => sum + s.correctCount, 0);
            const accuracy = totalQuestions > 0 ? Math.round((totalCorrect / totalQuestions) * 100) : 0;
            const totalScore = monthSessions.reduce((sum, s) => sum + s.score, 0);

            stats.push({
                month: `${monthStart.getFullYear()}-${(monthStart.getMonth() + 1).toString().padStart(2, '0')}`,
                sessions: monthSessions.length,
                questions: totalQuestions,
                correct: totalCorrect,
                accuracy: accuracy,
                score: totalScore
            });
        }

        return stats;
    }

    /**
     * 获取趋势数据
     */
    getTrends() {
        const recentSessions = this.getRecentSessions(30);
        const olderSessions = this.sessions.filter(session => {
            const sessionDate = new Date(session.startTime);
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            return sessionDate < thirtyDaysAgo;
        });

        const calculateAverage = (sessions, field) => {
            if (sessions.length === 0) return 0;
            return sessions.reduce((sum, s) => sum + s[field], 0) / sessions.length;
        };

        const recentAccuracy = this.calculateAccuracyForSessions(recentSessions);
        const olderAccuracy = this.calculateAccuracyForSessions(olderSessions);

        return {
            accuracyTrend: recentAccuracy - olderAccuracy,
            scoreTrend: calculateAverage(recentSessions, 'score') - calculateAverage(olderSessions, 'score'),
            sessionFrequency: recentSessions.length / 30 // 每天平均会话数
        };
    }

    /**
     * 计算会话组的正确率
     */
    calculateAccuracyForSessions(sessions) {
        if (sessions.length === 0) return 0;
        
        const totalQuestions = sessions.reduce((sum, s) => sum + s.totalCount, 0);
        const totalCorrect = sessions.reduce((sum, s) => sum + s.correctCount, 0);
        
        return totalQuestions > 0 ? (totalCorrect / totalQuestions) * 100 : 0;
    }

    /**
     * 更新成就
     */
    updateAchievements() {
        const user = storageManager.getUser();
        if (user) {
            this.achievements = user.achievements || [];
        }
    }

    /**
     * 导出进度数据
     */
    exportProgressData() {
        return {
            sessions: this.sessions,
            progressData: this.getProgressData(),
            statistics: this.calculateStatistics(),
            exportTime: new Date().toISOString()
        };
    }

    /**
     * 重置进度数据
     */
    resetProgressData() {
        this.sessions = [];
        storageManager.setSessions([]);
        
        // 重置用户成就
        const user = storageManager.getUser();
        if (user) {
            user.achievements = [];
            user.totalPoints = 0;
            storageManager.setUser(user);
        }
        
        console.log('进度数据已重置');
    }

    /**
     * 获取学习建议
     */
    getLearningRecommendations() {
        const progressData = this.getProgressData();
        const levelProgress = progressData.levelProgress;
        const recommendations = [];

        // 基于正确率的建议
        if (progressData.overallAccuracy < 70) {
            recommendations.push({
                type: 'accuracy',
                priority: 'high',
                message: '建议多练习基础题目，提高正确率',
                action: 'practice_basics'
            });
        }

        // 基于级别进度的建议
        Object.keys(levelProgress).forEach(level => {
            const progress = levelProgress[level];
            if (progress.sessions > 0 && progress.accuracy >= 80) {
                const nextLevel = this.getNextLevel(level);
                if (nextLevel) {
                    recommendations.push({
                        type: 'level',
                        priority: 'medium',
                        message: `${level}掌握良好，可以尝试${nextLevel}`,
                        action: 'unlock_level',
                        data: { nextLevel }
                    });
                }
            }
        });

        // 基于练习频率的建议
        if (progressData.streakDays === 0) {
            recommendations.push({
                type: 'frequency',
                priority: 'medium',
                message: '建议每天坚持练习，养成学习习惯',
                action: 'daily_practice'
            });
        }

        // 基于错题的建议
        const wrongQuestions = storageManager.getWrongQuestions();
        const activeWrongQuestions = wrongQuestions.filter(wq => !wq.masteredAt);
        if (activeWrongQuestions.length > 10) {
            recommendations.push({
                type: 'wrong_questions',
                priority: 'high',
                message: '错题较多，建议进行错题复训',
                action: 'review_wrong_questions'
            });
        }

        return recommendations.sort((a, b) => {
            const priorityOrder = { high: 3, medium: 2, low: 1 };
            return priorityOrder[b.priority] - priorityOrder[a.priority];
        });
    }

    /**
     * 获取下一个级别
     */
    getNextLevel(currentLevel) {
        const levelOrder = ['within-10', 'within-20', 'within-100', 'multiplication'];
        const currentIndex = levelOrder.indexOf(currentLevel);
        return currentIndex >= 0 && currentIndex < levelOrder.length - 1 ? 
            levelOrder[currentIndex + 1] : null;
    }
}

// 创建全局实例
const progressTracker = new ProgressTracker();