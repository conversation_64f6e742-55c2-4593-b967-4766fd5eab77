/**
 * 错题管理器
 * 负责错题的记录、管理和复训功能
 */

class WrongQuestionManager {
    constructor() {
        this.wrongQuestions = [];
        this.loadWrongQuestions();
    }

    /**
     * 加载错题数据
     */
    loadWrongQuestions() {
        const data = storageManager.getWrongQuestions();
        this.wrongQuestions = data.map(wq => new WrongQuestion(wq));
    }

    /**
     * 保存错题数据
     */
    saveWrongQuestions() {
        const data = this.wrongQuestions.map(wq => wq.toJSON());
        storageManager.setWrongQuestions(data);
    }

    /**
     * 添加错题
     */
    addWrongQuestion(question, userAnswer) {
        // 检查是否已存在相同题目
        const existingIndex = this.wrongQuestions.findIndex(wq => 
            wq.question.operand1 === question.operand1 &&
            wq.question.operand2 === question.operand2 &&
            wq.question.type === question.type
        );

        if (existingIndex >= 0) {
            // 更新现有错题
            this.wrongQuestions[existingIndex].markWrong();
        } else {
            // 添加新错题
            const wrongQuestion = new WrongQuestion({
                question: question,
                userAnswer: userAnswer,
                correctAnswer: question.correctAnswer
            });
            this.wrongQuestions.push(wrongQuestion);
        }

        this.saveWrongQuestions();
        console.log('添加错题:', question.getExpression());
    }

    /**
     * 标记错题为正确
     */
    markAsCorrect(questionId) {
        const wrongQuestion = this.wrongQuestions.find(wq => wq.id === questionId);
        if (wrongQuestion) {
            wrongQuestion.markCorrect();
            this.saveWrongQuestions();
            
            if (wrongQuestion.isMastered()) {
                console.log('错题已掌握:', wrongQuestion.question.getExpression());
            }
        }
    }

    /**
     * 获取活跃错题（未掌握的）
     */
    getActiveWrongQuestions() {
        return this.wrongQuestions.filter(wq => !wq.isMastered());
    }

    /**
     * 获取已掌握错题
     */
    getMasteredQuestions() {
        return this.wrongQuestions.filter(wq => wq.isMastered());
    }

    /**
     * 获取所有错题
     */
    getAllWrongQuestions() {
        return [...this.wrongQuestions];
    }

    /**
     * 删除错题
     */
    removeWrongQuestion(questionId) {
        const index = this.wrongQuestions.findIndex(wq => wq.id === questionId);
        if (index >= 0) {
            this.wrongQuestions.splice(index, 1);
            this.saveWrongQuestions();
            return true;
        }
        return false;
    }

    /**
     * 生成复训会话
     */
    generateReviewSession(count = 20) {
        const activeWrongQuestions = this.getActiveWrongQuestions();
        
        if (activeWrongQuestions.length === 0) {
            return [];
        }

        // 按错误次数和最后错误时间排序，优先复习错误次数多的和最近错误的
        const sortedQuestions = activeWrongQuestions.sort((a, b) => {
            const scoreA = a.wrongCount * 2 + (Date.now() - new Date(a.lastWrongAt)) / (1000 * 60 * 60 * 24);
            const scoreB = b.wrongCount * 2 + (Date.now() - new Date(b.lastWrongAt)) / (1000 * 60 * 60 * 24);
            return scoreB - scoreA;
        });

        const reviewQuestions = [];
        const selectedQuestions = sortedQuestions.slice(0, Math.min(count, sortedQuestions.length));

        // 为每个错题生成1-2个变体
        selectedQuestions.forEach(wrongQ => {
            // 添加原题
            reviewQuestions.push(new Question(wrongQ.question));
            
            // 添加相似题目
            if (reviewQuestions.length < count) {
                const similarQuestion = this.generateSimilarQuestion(wrongQ.question);
                reviewQuestions.push(similarQuestion);
            }
        });

        // 如果还不够，重复添加高优先级题目
        while (reviewQuestions.length < count && selectedQuestions.length > 0) {
            const randomWrongQ = selectedQuestions[Math.floor(Math.random() * selectedQuestions.length)];
            reviewQuestions.push(new Question(randomWrongQ.question));
        }

        // 打乱顺序
        return this.shuffleArray(reviewQuestions.slice(0, count));
    }

    /**
     * 生成相似题目
     */
    generateSimilarQuestion(originalQuestion) {
        const variation = Math.floor(Math.random() * 5) - 2; // -2 到 2 的变化
        let operand1 = Math.max(0, originalQuestion.operand1 + variation);
        let operand2 = Math.max(0, originalQuestion.operand2 + variation);
        
        // 确保结果合理
        if (originalQuestion.type === 'subtraction') {
            operand2 = Math.min(operand2, operand1);
        }
        
        // 根据原题目的类别限制范围
        if (originalQuestion.category === 'within-10') {
            operand1 = Math.min(operand1, 10);
            operand2 = Math.min(operand2, 10);
        } else if (originalQuestion.category === 'within-20') {
            operand1 = Math.min(operand1, 20);
            operand2 = Math.min(operand2, 20);
        } else if (originalQuestion.category === 'multiplication') {
            operand1 = Math.max(1, Math.min(operand1, 9));
            operand2 = Math.max(1, Math.min(operand2, 9));
        }
        
        return new Question({
            type: originalQuestion.type,
            operand1,
            operand2,
            category: originalQuestion.category,
            difficulty: originalQuestion.difficulty
        });
    }

    /**
     * 打乱数组顺序
     */
    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }

    /**
     * 获取错题统计
     */
    getStatistics() {
        const active = this.getActiveWrongQuestions();
        const mastered = this.getMasteredQuestions();
        
        const stats = {
            totalWrongQuestions: this.wrongQuestions.length,
            activeWrongQuestions: active.length,
            masteredQuestions: mastered.length,
            masteryRate: this.wrongQuestions.length > 0 ? 
                Math.round((mastered.length / this.wrongQuestions.length) * 100) : 0
        };

        // 按类型统计
        const typeStats = {};
        this.wrongQuestions.forEach(wq => {
            const type = wq.question.type;
            if (!typeStats[type]) {
                typeStats[type] = { total: 0, active: 0, mastered: 0 };
            }
            typeStats[type].total++;
            if (wq.isMastered()) {
                typeStats[type].mastered++;
            } else {
                typeStats[type].active++;
            }
        });
        stats.byType = typeStats;

        // 按难度统计
        const difficultyStats = {};
        this.wrongQuestions.forEach(wq => {
            const difficulty = wq.question.difficulty || 1;
            if (!difficultyStats[difficulty]) {
                difficultyStats[difficulty] = { total: 0, active: 0, mastered: 0 };
            }
            difficultyStats[difficulty].total++;
            if (wq.isMastered()) {
                difficultyStats[difficulty].mastered++;
            } else {
                difficultyStats[difficulty].active++;
            }
        });
        stats.byDifficulty = difficultyStats;

        return stats;
    }

    /**
     * 清理已掌握的旧错题
     */
    cleanupMasteredQuestions(daysOld = 30) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysOld);

        const initialCount = this.wrongQuestions.length;
        this.wrongQuestions = this.wrongQuestions.filter(wq => {
            if (wq.isMastered() && new Date(wq.masteredAt) < cutoffDate) {
                return false; // 删除旧的已掌握错题
            }
            return true;
        });

        const removedCount = initialCount - this.wrongQuestions.length;
        if (removedCount > 0) {
            this.saveWrongQuestions();
            console.log(`清理了 ${removedCount} 个旧的已掌握错题`);
        }

        return removedCount;
    }

    /**
     * 导出错题数据
     */
    exportWrongQuestions() {
        return {
            wrongQuestions: this.wrongQuestions.map(wq => wq.toJSON()),
            statistics: this.getStatistics(),
            exportTime: new Date().toISOString()
        };
    }

    /**
     * 导入错题数据
     */
    importWrongQuestions(data) {
        try {
            if (data.wrongQuestions && Array.isArray(data.wrongQuestions)) {
                this.wrongQuestions = data.wrongQuestions.map(wq => new WrongQuestion(wq));
                this.saveWrongQuestions();
                console.log(`导入了 ${this.wrongQuestions.length} 个错题`);
                return true;
            }
        } catch (error) {
            console.error('导入错题数据失败:', error);
        }
        return false;
    }

    /**
     * 重置所有错题数据
     */
    resetAllWrongQuestions() {
        this.wrongQuestions = [];
        this.saveWrongQuestions();
        console.log('已重置所有错题数据');
    }

    /**
     * 获取需要重点复习的错题
     */
    getPriorityReviewQuestions(limit = 10) {
        const activeQuestions = this.getActiveWrongQuestions();
        
        // 计算优先级分数：错误次数 * 2 + 天数权重
        const scoredQuestions = activeQuestions.map(wq => {
            const daysSinceLastWrong = Math.floor((Date.now() - new Date(wq.lastWrongAt)) / (1000 * 60 * 60 * 24));
            const score = wq.wrongCount * 2 + Math.max(0, 7 - daysSinceLastWrong); // 最近7天内的错题有额外权重
            return { question: wq, score };
        });

        // 按分数排序并返回前N个
        return scoredQuestions
            .sort((a, b) => b.score - a.score)
            .slice(0, limit)
            .map(item => item.question);
    }
}

// 创建全局实例
const wrongQuestionManager = new WrongQuestionManager();