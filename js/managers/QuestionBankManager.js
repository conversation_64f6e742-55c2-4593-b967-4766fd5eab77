/**
 * 题库管理器
 * 负责生成不同难度级别的数学题目
 */

class QuestionBankManager {
    constructor() {
        this.questionBanks = {
            'within-10': {
                name: '10以内加减法',
                description: '数字范围0-10的加减法练习，包含所有基础组合',
                types: ['addition', 'subtraction'],
                range: { min: 0, max: 10 },
                difficulty: 1,
                features: ['基础运算', '数感培养', '心算训练']
            },
            'within-20': {
                name: '20以内加减法',
                description: '数字范围0-20的加减法练习，包含进位退位',
                types: ['addition', 'subtraction'],
                range: { min: 0, max: 20 },
                difficulty: 2,
                features: ['进位运算', '退位运算', '数位概念']
            },
            'within-100': {
                name: '100以内加减法',
                description: '数字范围0-100的加减法练习，包含两位数运算',
                types: ['addition', 'subtraction'],
                range: { min: 0, max: 100 },
                difficulty: 3,
                features: ['两位数运算', '进位退位', '数位分解']
            },
            'within-1000': {
                name: '1000以内加减法',
                description: '数字范围0-1000的加减法练习，包含三位数运算',
                types: ['addition', 'subtraction'],
                range: { min: 0, max: 1000 },
                difficulty: 4,
                features: ['三位数运算', '多位进位', '大数概念']
            },
            'multiplication': {
                name: '乘法表',
                description: '九九乘法表练习，包含所有1-9的乘法组合',
                types: ['multiplication'],
                range: { min: 1, max: 9 },
                difficulty: 2,
                features: ['九九乘法表', '乘法口诀', '倍数概念']
            },
            'division': {
                name: '除法练习',
                description: '基础除法练习，包含整除和有余数除法',
                types: ['division'],
                range: { min: 1, max: 81 },
                difficulty: 3,
                features: ['整除运算', '余数概念', '逆向思维']
            }
        };
    }

    /**
     * 获取可用的题库列表
     */
    getAvailableBanks() {
        return Object.keys(this.questionBanks).map(id => ({
            id,
            ...this.questionBanks[id]
        }));
    }

    /**
     * 获取指定题库信息
     */
    getBankInfo(bankId) {
        return this.questionBanks[bankId] || null;
    }

    /**
     * 生成指定数量的题目
     */
    generateQuestions(bankId, count = 50) {
        const bank = this.questionBanks[bankId];
        if (!bank) {
            throw new Error(`未找到题库: ${bankId}`);
        }

        const questions = [];
        const difficultyLevels = this.getDifficultyProgression(count);

        for (let i = 0; i < count; i++) {
            const difficulty = difficultyLevels[i];
            const question = this.generateSingleQuestion(bankId, difficulty);
            questions.push(question);
        }

        return questions;
    }

    /**
     * 生成单个题目
     */
    generateSingleQuestion(bankId, difficulty = 1) {
        const bank = this.questionBanks[bankId];
        if (!bank) {
            throw new Error(`未找到题库: ${bankId}`);
        }

        const type = this.getRandomType(bank.types);
        let question;

        switch (bankId) {
            case 'within-10':
                question = this.generateWithin10Question(type, difficulty);
                break;
            case 'within-20':
                question = this.generateWithin20Question(type, difficulty);
                break;
            case 'within-100':
                question = this.generateWithin100Question(type, difficulty);
                break;
            case 'within-1000':
                question = this.generateWithin1000Question(type, difficulty);
                break;
            case 'multiplication':
                question = this.generateMultiplicationQuestion(difficulty);
                break;
            case 'division':
                question = this.generateDivisionQuestion(difficulty);
                break;
            default:
                throw new Error(`不支持的题库类型: ${bankId}`);
        }

        question.category = bankId;
        question.difficulty = difficulty;
        return question;
    }

    /**
     * 生成10以内加减法题目
     */
    generateWithin10Question(type, difficulty) {
        let operand1, operand2;

        if (type === 'addition') {
            // 根据难度调整，确保结果不超过10
            if (difficulty === 1) {
                // 简单：结果0-5
                operand1 = this.getRandomNumber(0, 5);
                operand2 = this.getRandomNumber(0, Math.min(5, 10 - operand1));
            } else if (difficulty === 2) {
                // 中等：结果6-8
                operand1 = this.getRandomNumber(3, 8);
                operand2 = this.getRandomNumber(Math.max(0, 6 - operand1), Math.min(8 - operand1, 10 - operand1));
            } else {
                // 困难：结果9-10
                operand1 = this.getRandomNumber(4, 10);
                operand2 = this.getRandomNumber(Math.max(0, 9 - operand1), 10 - operand1);
            }
        } else {
            // 减法：确保结果非负
            if (difficulty === 1) {
                // 简单：被减数2-6
                operand1 = this.getRandomNumber(2, 6);
                operand2 = this.getRandomNumber(0, operand1);
            } else if (difficulty === 2) {
                // 中等：被减数5-8
                operand1 = this.getRandomNumber(5, 8);
                operand2 = this.getRandomNumber(1, operand1);
            } else {
                // 困难：被减数7-10
                operand1 = this.getRandomNumber(7, 10);
                operand2 = this.getRandomNumber(2, operand1);
            }
        }

        return new Question({
            type,
            operand1,
            operand2,
            category: 'within-10'
        });
    }

    /**
     * 生成20以内加减法题目
     */
    generateWithin20Question(type, difficulty) {
        let operand1, operand2;

        if (type === 'addition') {
            if (difficulty === 1) {
                // 简单：结果11-15，包含进位
                operand1 = this.getRandomNumber(5, 10);
                operand2 = this.getRandomNumber(Math.max(1, 11 - operand1), Math.min(10, 15 - operand1));
            } else if (difficulty === 2) {
                // 中等：结果16-18
                operand1 = this.getRandomNumber(7, 15);
                operand2 = this.getRandomNumber(Math.max(1, 16 - operand1), Math.min(11, 18 - operand1));
            } else {
                // 困难：结果19-20
                operand1 = this.getRandomNumber(9, 19);
                operand2 = this.getRandomNumber(Math.max(1, 19 - operand1), 20 - operand1);
            }
        } else {
            if (difficulty === 1) {
                // 简单：被减数11-15
                operand1 = this.getRandomNumber(11, 15);
                operand2 = this.getRandomNumber(1, Math.min(operand1, 10));
            } else if (difficulty === 2) {
                // 中等：被减数16-18，包含退位
                operand1 = this.getRandomNumber(16, 18);
                operand2 = this.getRandomNumber(3, operand1 - 10);
            } else {
                // 困难：被减数19-20
                operand1 = this.getRandomNumber(19, 20);
                operand2 = this.getRandomNumber(5, operand1 - 10);
            }
        }

        return new Question({
            type,
            operand1,
            operand2,
            category: 'within-20'
        });
    }

    /**
     * 生成100以内加减法题目
     */
    generateWithin100Question(type, difficulty) {
        let operand1, operand2;

        if (type === 'addition') {
            if (difficulty === 1) {
                // 简单：结果21-50
                operand1 = this.getRandomNumber(10, 30);
                operand2 = this.getRandomNumber(Math.max(1, 21 - operand1), Math.min(40, 50 - operand1));
            } else if (difficulty === 2) {
                // 中等：结果51-80
                operand1 = this.getRandomNumber(20, 60);
                operand2 = this.getRandomNumber(Math.max(1, 51 - operand1), Math.min(60, 80 - operand1));
            } else {
                // 困难：结果81-100
                operand1 = this.getRandomNumber(30, 80);
                operand2 = this.getRandomNumber(Math.max(1, 81 - operand1), 100 - operand1);
            }
        } else {
            if (difficulty === 1) {
                // 简单：被减数21-50
                operand1 = this.getRandomNumber(21, 50);
                operand2 = this.getRandomNumber(1, Math.min(operand1 - 1, 20));
            } else if (difficulty === 2) {
                // 中等：被减数51-80
                operand1 = this.getRandomNumber(51, 80);
                operand2 = this.getRandomNumber(10, Math.min(operand1 - 10, 50));
            } else {
                // 困难：被减数81-100
                operand1 = this.getRandomNumber(81, 100);
                operand2 = this.getRandomNumber(20, Math.min(operand1 - 20, 70));
            }
        }

        return new Question({
            type,
            operand1,
            operand2,
            category: 'within-100'
        });
    }

    /**
     * 生成1000以内加减法题目
     */
    generateWithin1000Question(type, difficulty) {
        let operand1, operand2;

        if (type === 'addition') {
            if (difficulty === 1) {
                // 简单：结果101-300
                operand1 = this.getRandomNumber(50, 200);
                operand2 = this.getRandomNumber(Math.max(1, 101 - operand1), Math.min(250, 300 - operand1));
            } else if (difficulty === 2) {
                // 中等：结果301-600
                operand1 = this.getRandomNumber(100, 400);
                operand2 = this.getRandomNumber(Math.max(1, 301 - operand1), Math.min(500, 600 - operand1));
            } else if (difficulty === 3) {
                // 困难：结果601-800
                operand1 = this.getRandomNumber(200, 600);
                operand2 = this.getRandomNumber(Math.max(1, 601 - operand1), Math.min(600, 800 - operand1));
            } else {
                // 极难：结果801-1000
                operand1 = this.getRandomNumber(300, 800);
                operand2 = this.getRandomNumber(Math.max(1, 801 - operand1), 1000 - operand1);
            }
        } else {
            if (difficulty === 1) {
                // 简单：被减数101-300
                operand1 = this.getRandomNumber(101, 300);
                operand2 = this.getRandomNumber(1, Math.min(operand1 - 1, 100));
            } else if (difficulty === 2) {
                // 中等：被减数301-600
                operand1 = this.getRandomNumber(301, 600);
                operand2 = this.getRandomNumber(50, Math.min(operand1 - 50, 300));
            } else if (difficulty === 3) {
                // 困难：被减数601-800
                operand1 = this.getRandomNumber(601, 800);
                operand2 = this.getRandomNumber(100, Math.min(operand1 - 100, 500));
            } else {
                // 极难：被减数801-1000
                operand1 = this.getRandomNumber(801, 1000);
                operand2 = this.getRandomNumber(200, Math.min(operand1 - 200, 700));
            }
        }

        return new Question({
            type,
            operand1,
            operand2,
            category: 'within-1000'
        });
    }

    /**
     * 生成除法题目
     */
    generateDivisionQuestion(difficulty) {
        let dividend, divisor;

        if (difficulty === 1) {
            // 简单：除以1和简单整除
            if (Math.random() < 0.3) {
                // 30%概率：除以1
                divisor = 1;
                dividend = this.getRandomNumber(1, 20);
            } else {
                // 70%概率：除以2-5的整除
                divisor = this.getRandomNumber(2, 5);
                const quotient = this.getRandomNumber(2, 9);
                dividend = divisor * quotient;
            }
        } else if (difficulty === 2) {
            // 中等：除以2-9的整除
            divisor = this.getRandomNumber(2, 9);
            const quotient = this.getRandomNumber(2, 12);
            dividend = divisor * quotient;
        } else if (difficulty === 3) {
            // 困难：有余数的除法
            divisor = this.getRandomNumber(2, 9);
            const quotient = this.getRandomNumber(3, 15);
            const remainder = this.getRandomNumber(1, divisor - 1); // 确保有余数
            dividend = divisor * quotient + remainder;
        } else {
            // 极难：大数有余数除法
            divisor = this.getRandomNumber(6, 12);
            const quotient = this.getRandomNumber(8, 20);
            const remainder = this.getRandomNumber(0, divisor - 1);
            dividend = divisor * quotient + remainder;
        }

        return new Question({
            type: 'division',
            operand1: dividend,
            operand2: divisor,
            category: 'division'
        });
    }

    /**
     * 生成乘法表题目
     */
    generateMultiplicationQuestion(difficulty) {
        let operand1, operand2;

        if (difficulty === 1) {
            // 简单：1的乘法和简单倍数 (1, 2, 5, 10)
            const easyNumbers = [1, 2, 5, 10];
            operand1 = easyNumbers[Math.floor(Math.random() * easyNumbers.length)];
            operand2 = this.getRandomNumber(1, 9);
        } else if (difficulty === 2) {
            // 中等：3, 4, 6的乘法
            const mediumNumbers = [3, 4, 6];
            operand1 = mediumNumbers[Math.floor(Math.random() * mediumNumbers.length)];
            operand2 = this.getRandomNumber(1, 9);
        } else {
            // 困难：7, 8, 9的乘法
            const hardNumbers = [7, 8, 9];
            operand1 = hardNumbers[Math.floor(Math.random() * hardNumbers.length)];
            operand2 = this.getRandomNumber(1, 9);
        }

        return new Question({
            type: 'multiplication',
            operand1,
            operand2,
            category: 'multiplication'
        });
    }

    /**
     * 获取难度递进序列
     */
    getDifficultyProgression(totalCount) {
        const progression = [];
        
        // 前10题：简单难度
        for (let i = 0; i < Math.min(10, totalCount); i++) {
            progression.push(1);
        }
        
        // 第11-30题：中等难度
        for (let i = 10; i < Math.min(30, totalCount); i++) {
            progression.push(2);
        }
        
        // 第31-50题：困难难度
        for (let i = 30; i < totalCount; i++) {
            progression.push(3);
        }
        
        return progression;
    }

    /**
     * 随机选择题目类型
     */
    getRandomType(types) {
        return types[Math.floor(Math.random() * types.length)];
    }

    /**
     * 生成指定范围内的随机数
     */
    getRandomNumber(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    /**
     * 验证题目是否符合要求
     */
    validateQuestion(question, bankId) {
        const bank = this.questionBanks[bankId];
        if (!bank) return false;

        // 检查题目类型
        if (!bank.types.includes(question.type)) return false;

        // 检查数值范围
        const { min, max } = bank.range;
        if (question.operand1 < min || question.operand1 > max) return false;
        if (question.operand2 < min || question.operand2 > max) return false;

        // 检查结果合理性
        if (question.type === 'subtraction' && question.correctAnswer < 0) return false;
        if (question.type === 'addition' && bankId !== 'within-100' && question.correctAnswer > max) return false;

        return true;
    }

    /**
     * 生成复习题目（基于错题）
     */
    generateReviewQuestions(wrongQuestions, count = 20) {
        const reviewQuestions = [];
        
        // 如果错题不足，生成相似题目
        while (reviewQuestions.length < count) {
            if (wrongQuestions.length === 0) break;
            
            const wrongQ = wrongQuestions[Math.floor(Math.random() * wrongQuestions.length)];
            const originalQuestion = wrongQ.question;
            
            // 生成相似题目
            const similarQuestion = this.generateSimilarQuestion(originalQuestion);
            reviewQuestions.push(similarQuestion);
            
            // 也包含原题
            if (reviewQuestions.length < count) {
                reviewQuestions.push(new Question(originalQuestion));
            }
        }
        
        // 打乱顺序
        return this.shuffleArray(reviewQuestions);
    }

    /**
     * 生成相似题目
     */
    generateSimilarQuestion(originalQuestion) {
        const variation = this.getRandomNumber(-2, 2);
        let operand1 = Math.max(0, originalQuestion.operand1 + variation);
        let operand2 = Math.max(0, originalQuestion.operand2 + variation);
        
        // 确保结果合理
        if (originalQuestion.type === 'subtraction') {
            operand2 = Math.min(operand2, operand1);
        }
        
        return new Question({
            type: originalQuestion.type,
            operand1,
            operand2,
            category: originalQuestion.category,
            difficulty: originalQuestion.difficulty
        });
    }

    /**
     * 打乱数组顺序
     */
    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }

    /**
     * 获取题库统计信息
     */
    getBankStats(bankId) {
        const bank = this.questionBanks[bankId];
        if (!bank) return null;

        return {
            name: bank.name,
            description: bank.description,
            types: bank.types,
            difficulty: bank.difficulty,
            estimatedQuestions: this.estimateQuestionCount(bankId)
        };
    }

    /**
     * 估算题库可生成的题目数量
     */
    estimateQuestionCount(bankId) {
        const bank = this.questionBanks[bankId];
        if (!bank) return 0;

        const { min, max } = bank.range;
        const rangeSize = max - min + 1;
        
        let combinations = 0;
        bank.types.forEach(type => {
            if (type === 'addition') {
                // 加法组合数
                combinations += rangeSize * rangeSize;
            } else if (type === 'subtraction') {
                // 减法组合数（确保结果非负）
                for (let i = min; i <= max; i++) {
                    combinations += i - min + 1;
                }
            } else if (type === 'multiplication') {
                combinations += rangeSize * rangeSize;
            }
        });
        
        return combinations;
    }
}

// 创建全局实例
const questionBankManager = new QuestionBankManager();