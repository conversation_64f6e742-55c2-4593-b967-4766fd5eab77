/**
 * 账号管理器
 * 负责多用户账号的创建、切换和管理
 */

class AccountManager {
    constructor() {
        this.currentAccountId = null;
        this.accounts = {};
        this.loadAccounts();
    }

    /**
     * 加载所有账号数据
     */
    loadAccounts() {
        try {
            const accountsData = localStorage.getItem('mathTraining_accounts');
            if (accountsData) {
                this.accounts = JSON.parse(accountsData);
            }

            // 获取当前账号ID
            this.currentAccountId = localStorage.getItem('mathTraining_currentAccount') || 'default';

            // 如果没有默认账号，创建一个
            if (!this.accounts[this.currentAccountId]) {
                this.createAccount('默认用户', this.currentAccountId);
            }

            console.log('账号数据加载完成，当前账号:', this.currentAccountId);
        } catch (error) {
            console.error('加载账号数据失败:', error);
            this.createAccount('默认用户', 'default');
            this.currentAccountId = 'default';
        }
    }

    /**
     * 保存账号数据
     */
    saveAccounts() {
        try {
            localStorage.setItem('mathTraining_accounts', JSON.stringify(this.accounts));
            localStorage.setItem('mathTraining_currentAccount', this.currentAccountId);
        } catch (error) {
            console.error('保存账号数据失败:', error);
        }
    }

    /**
     * 创建新账号
     */
    createAccount(name, accountId = null) {
        if (!name || name.trim() === '') {
            throw new Error('账号名称不能为空');
        }

        // 生成账号ID
        if (!accountId) {
            accountId = 'account_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        // 检查账号是否已存在
        if (this.accounts[accountId]) {
            throw new Error('账号已存在');
        }

        // 创建账号数据结构
        const accountData = {
            id: accountId,
            name: name.trim(),
            createdAt: new Date().toISOString(),
            lastActiveAt: new Date().toISOString(),
            // 用户数据
            user: {
                id: accountId,
                name: name.trim(),
                level: 1,
                totalPoints: 0,
                achievements: [],
                unlockedLevels: ['within-10'],
                createdAt: new Date(),
                lastActiveAt: new Date()
            },
            // 练习记录
            sessions: [],
            // 错题数据
            wrongQuestions: [],
            // 设置数据
            settings: {
                soundEnabled: true,
                animationEnabled: true,
                theme: 'default',
                difficulty: 'normal'
            }
        };

        this.accounts[accountId] = accountData;
        this.saveAccounts();

        console.log('创建账号成功:', name, accountId);
        return accountId;
    }

    /**
     * 切换账号
     */
    switchAccount(accountId) {
        if (!this.accounts[accountId]) {
            throw new Error('账号不存在');
        }

        // 保存当前账号的数据
        this.saveCurrentAccountData();

        // 切换到新账号
        this.currentAccountId = accountId;
        this.accounts[accountId].lastActiveAt = new Date().toISOString();
        
        // 加载新账号的数据到存储管理器
        this.loadAccountData(accountId);
        
        this.saveAccounts();
        
        console.log('切换账号成功:', accountId);
        
        // 触发账号切换事件
        this.triggerAccountSwitchEvent(accountId);
        
        return true;
    }

    /**
     * 保存当前账号数据
     */
    saveCurrentAccountData() {
        if (!this.currentAccountId || !this.accounts[this.currentAccountId]) {
            return;
        }

        const currentAccount = this.accounts[this.currentAccountId];
        
        // 保存用户数据
        const userData = storageManager.getUser();
        if (userData) {
            currentAccount.user = userData;
        }

        // 保存练习记录
        currentAccount.sessions = storageManager.getSessions();

        // 保存错题数据
        currentAccount.wrongQuestions = storageManager.getWrongQuestions();

        // 保存设置数据
        currentAccount.settings = storageManager.getSettings();

        console.log('当前账号数据已保存:', this.currentAccountId);
    }

    /**
     * 加载账号数据到存储管理器
     */
    loadAccountData(accountId) {
        const accountData = this.accounts[accountId];
        if (!accountData) {
            throw new Error('账号数据不存在');
        }

        // 加载用户数据
        storageManager.setUser(accountData.user);

        // 加载练习记录
        storageManager.setSessions(accountData.sessions || []);

        // 加载错题数据
        storageManager.setWrongQuestions(accountData.wrongQuestions || []);

        // 加载设置数据
        storageManager.setSettings(accountData.settings || {});

        console.log('账号数据已加载:', accountId);
    }

    /**
     * 删除账号
     */
    deleteAccount(accountId) {
        if (!this.accounts[accountId]) {
            throw new Error('账号不存在');
        }

        if (accountId === this.currentAccountId) {
            throw new Error('不能删除当前使用的账号');
        }

        if (Object.keys(this.accounts).length <= 1) {
            throw new Error('至少需要保留一个账号');
        }

        delete this.accounts[accountId];
        this.saveAccounts();

        console.log('删除账号成功:', accountId);
        return true;
    }

    /**
     * 重命名账号
     */
    renameAccount(accountId, newName) {
        if (!this.accounts[accountId]) {
            throw new Error('账号不存在');
        }

        if (!newName || newName.trim() === '') {
            throw new Error('账号名称不能为空');
        }

        this.accounts[accountId].name = newName.trim();
        
        // 如果是当前账号，同时更新用户数据中的名称
        if (accountId === this.currentAccountId) {
            this.accounts[accountId].user.name = newName.trim();
            storageManager.setUser(this.accounts[accountId].user);
        }

        this.saveAccounts();

        console.log('重命名账号成功:', accountId, newName);
        return true;
    }

    /**
     * 获取当前账号信息
     */
    getCurrentAccount() {
        return this.accounts[this.currentAccountId] || null;
    }

    /**
     * 获取所有账号列表
     */
    getAllAccounts() {
        return Object.values(this.accounts).map(account => ({
            id: account.id,
            name: account.name,
            createdAt: account.createdAt,
            lastActiveAt: account.lastActiveAt,
            totalPoints: account.user.totalPoints || 0,
            sessionsCount: account.sessions ? account.sessions.length : 0,
            achievementsCount: account.user.achievements ? account.user.achievements.length : 0,
            isActive: account.id === this.currentAccountId
        }));
    }

    /**
     * 导出账号数据
     */
    exportAccountData(accountId = null) {
        const targetAccountId = accountId || this.currentAccountId;
        const accountData = this.accounts[targetAccountId];
        
        if (!accountData) {
            throw new Error('账号不存在');
        }

        // 如果是当前账号，先保存最新数据
        if (targetAccountId === this.currentAccountId) {
            this.saveCurrentAccountData();
        }

        const exportData = {
            account: {
                name: accountData.name,
                createdAt: accountData.createdAt
            },
            user: accountData.user,
            sessions: accountData.sessions,
            wrongQuestions: accountData.wrongQuestions,
            settings: accountData.settings,
            exportTime: new Date().toISOString(),
            version: '1.0.0'
        };

        return JSON.stringify(exportData, null, 2);
    }

    /**
     * 导入账号数据
     */
    importAccountData(jsonData, accountName = null) {
        try {
            const importData = JSON.parse(jsonData);
            
            // 验证数据格式
            if (!importData.user || !importData.account) {
                throw new Error('数据格式不正确');
            }

            // 生成新的账号ID
            const accountId = this.createAccount(
                accountName || importData.account.name || '导入账号'
            );

            // 导入数据
            const accountData = this.accounts[accountId];
            accountData.user = importData.user;
            accountData.sessions = importData.sessions || [];
            accountData.wrongQuestions = importData.wrongQuestions || [];
            accountData.settings = importData.settings || {};

            // 更新用户ID
            accountData.user.id = accountId;

            this.saveAccounts();

            console.log('导入账号数据成功:', accountId);
            return accountId;
        } catch (error) {
            console.error('导入账号数据失败:', error);
            throw new Error('导入失败: ' + error.message);
        }
    }

    /**
     * 清空账号数据
     */
    clearAccountData(accountId = null) {
        const targetAccountId = accountId || this.currentAccountId;
        const accountData = this.accounts[targetAccountId];
        
        if (!accountData) {
            throw new Error('账号不存在');
        }

        // 重置账号数据
        accountData.user = {
            id: targetAccountId,
            name: accountData.name,
            level: 1,
            totalPoints: 0,
            achievements: [],
            unlockedLevels: ['within-10'],
            createdAt: new Date(),
            lastActiveAt: new Date()
        };
        accountData.sessions = [];
        accountData.wrongQuestions = [];
        accountData.settings = {
            soundEnabled: true,
            animationEnabled: true,
            theme: 'default',
            difficulty: 'normal'
        };

        // 如果是当前账号，同时清空存储管理器
        if (targetAccountId === this.currentAccountId) {
            this.loadAccountData(targetAccountId);
        }

        this.saveAccounts();

        console.log('清空账号数据成功:', targetAccountId);
        return true;
    }

    /**
     * 获取账号统计信息
     */
    getAccountStats(accountId = null) {
        const targetAccountId = accountId || this.currentAccountId;
        const accountData = this.accounts[targetAccountId];
        
        if (!accountData) {
            return null;
        }

        // 如果是当前账号，先保存最新数据
        if (targetAccountId === this.currentAccountId) {
            this.saveCurrentAccountData();
        }

        const sessions = accountData.sessions || [];
        const wrongQuestions = accountData.wrongQuestions || [];
        
        return {
            name: accountData.name,
            totalPoints: accountData.user.totalPoints || 0,
            totalSessions: sessions.length,
            totalQuestions: sessions.reduce((sum, s) => sum + (s.totalCount || 0), 0),
            totalCorrect: sessions.reduce((sum, s) => sum + (s.correctCount || 0), 0),
            overallAccuracy: sessions.length > 0 ? 
                Math.round((sessions.reduce((sum, s) => sum + (s.correctCount || 0), 0) / 
                           sessions.reduce((sum, s) => sum + (s.totalCount || 0), 0)) * 100) : 0,
            achievementsCount: accountData.user.achievements ? accountData.user.achievements.length : 0,
            activeWrongQuestions: wrongQuestions.filter(wq => !wq.masteredAt).length,
            masteredQuestions: wrongQuestions.filter(wq => wq.masteredAt).length,
            createdAt: accountData.createdAt,
            lastActiveAt: accountData.lastActiveAt
        };
    }

    /**
     * 触发账号切换事件
     */
    triggerAccountSwitchEvent(accountId) {
        const event = new CustomEvent('accountSwitched', {
            detail: { accountId, accountData: this.accounts[accountId] }
        });
        window.dispatchEvent(event);
    }

    /**
     * 初始化当前账号
     */
    initializeCurrentAccount() {
        if (this.currentAccountId && this.accounts[this.currentAccountId]) {
            this.loadAccountData(this.currentAccountId);
            return true;
        }
        return false;
    }

    /**
     * 获取账号数量
     */
    getAccountCount() {
        return Object.keys(this.accounts).length;
    }

    /**
     * 检查账号名称是否可用
     */
    isAccountNameAvailable(name, excludeAccountId = null) {
        const trimmedName = name.trim();
        return !Object.values(this.accounts).some(account => 
            account.name === trimmedName && account.id !== excludeAccountId
        );
    }
}

// 创建全局实例
const accountManager = new AccountManager();