/**
 * 应用主控制器
 * 负责视图切换、事件处理和应用状态管理
 */

class AppController {
    constructor() {
        this.currentView = 'main-menu';
        this.views = {
            'main-menu': document.getElementById('main-menu'),
            'exercise-view': document.getElementById('exercise-view'),
            'result-view': document.getElementById('result-view'),
            'wrong-questions-view': document.getElementById('wrong-questions-view'),
            'progress-view': document.getElementById('progress-view'),
            'account-view': document.getElementById('account-view')
        };
        
        this.isInitialized = false;
        this.currentTimer = null;
        this.autoSubmitTimer = null;
        this.exerciseStartTime = null;
    }

    /**
     * 初始化应用
     */
    init() {
        try {
            this.setupEventListeners();
            this.updateMainMenu();
            this.switchView('main-menu');
            this.isInitialized = true;
            
            console.log('应用控制器初始化完成');
        } catch (error) {
            console.error('应用控制器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 主菜单事件
        this.setupMainMenuEvents();
        
        // 练习界面事件
        this.setupExerciseEvents();
        
        // 结果界面事件
        this.setupResultEvents();
        
        // 错题本事件
        this.setupWrongQuestionsEvents();
        
        // 进度界面事件
        this.setupProgressEvents();
        
        // 账号界面事件
        this.setupAccountEvents();
        
        // 全局事件
        this.setupGlobalEvents();
    }

    /**
     * 设置主菜单事件
     */
    setupMainMenuEvents() {
        // 难度级别选择
        const levelButtons = document.querySelectorAll('.level-btn');
        levelButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const level = e.target.dataset.level;
                this.startExercise(level);
            });
        });

        // 错题复训按钮
        const wrongQuestionsBtn = document.getElementById('wrong-questions-btn');
        wrongQuestionsBtn.addEventListener('click', () => {
            this.showWrongQuestions();
        });

        // 学习进度按钮
        const progressBtn = document.getElementById('progress-btn');
        progressBtn.addEventListener('click', () => {
            this.showProgress();
        });

        // 账号设置按钮
        const accountBtn = document.getElementById('account-btn');
        accountBtn.addEventListener('click', () => {
            this.showAccountSettings();
        });
    }

    /**
     * 设置练习界面事件
     */
    setupExerciseEvents() {
        // 提交答案按钮
        const submitBtn = document.getElementById('submit-answer');
        submitBtn.addEventListener('click', () => {
            this.submitAnswer();
        });

        // 提示按钮
        const hintBtn = document.getElementById('hint-btn');
        hintBtn.addEventListener('click', () => {
            this.showHint();
        });

        // 跳过按钮
        const skipBtn = document.getElementById('skip-btn');
        skipBtn.addEventListener('click', () => {
            this.skipCurrentQuestion();
        });

        // 答案输入框回车事件
        const answerInput = document.getElementById('answer-input');
        answerInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.submitAnswer();
            }
        });

        // 答案输入框焦点事件
        answerInput.addEventListener('focus', () => {
            answerInput.select();
        });

        // 答案输入框输入事件（实时验证和自动提交）
        answerInput.addEventListener('input', (e) => {
            this.validateInput(e.target);
            this.checkAutoSubmit(e.target);
        });

        // 设置触摸支持
        this.setupTouchSupport();
    }

    /**
     * 设置结果界面事件
     */
    setupResultEvents() {
        // 查看错题按钮
        const reviewWrongBtn = document.getElementById('review-wrong-btn');
        reviewWrongBtn.addEventListener('click', () => {
            this.showWrongQuestions();
        });

        // 继续练习按钮
        const continuePracticeBtn = document.getElementById('continue-practice-btn');
        continuePracticeBtn.addEventListener('click', () => {
            this.switchView('main-menu');
        });

        // 返回主菜单按钮
        const backToMenuBtn = document.getElementById('back-to-menu-btn');
        backToMenuBtn.addEventListener('click', () => {
            this.switchView('main-menu');
        });
    }

    /**
     * 设置错题本事件
     */
    setupWrongQuestionsEvents() {
        // 开始复训按钮
        const startReviewBtn = document.getElementById('start-review-btn');
        startReviewBtn.addEventListener('click', () => {
            this.startWrongQuestionReview();
        });

        // 返回按钮
        const backFromWrongBtn = document.getElementById('back-from-wrong-btn');
        backFromWrongBtn.addEventListener('click', () => {
            this.switchView('main-menu');
        });
    }

    /**
     * 设置进度界面事件
     */
    setupProgressEvents() {
        // 返回按钮
        const backFromProgressBtn = document.getElementById('back-from-progress-btn');
        backFromProgressBtn.addEventListener('click', () => {
            this.switchView('main-menu');
        });
    }

    /**
     * 设置账号界面事件
     */
    setupAccountEvents() {
        // 编辑账号按钮
        const editAccountBtn = document.getElementById('edit-account-btn');
        editAccountBtn.addEventListener('click', () => {
            this.editCurrentAccount();
        });

        // 创建账号按钮
        const createAccountBtn = document.getElementById('create-account-btn');
        createAccountBtn.addEventListener('click', () => {
            this.createNewAccount();
        });

        // 切换账号按钮
        const switchAccountBtn = document.getElementById('switch-account-btn');
        switchAccountBtn.addEventListener('click', () => {
            this.showAccountSwitcher();
        });

        // 删除账号按钮
        const deleteAccountBtn = document.getElementById('delete-account-btn');
        deleteAccountBtn.addEventListener('click', () => {
            this.deleteCurrentAccount();
        });

        // 导出数据按钮
        const exportDataBtn = document.getElementById('export-data-btn');
        exportDataBtn.addEventListener('click', () => {
            this.exportAccountData();
        });

        // 导入数据按钮
        const importDataBtn = document.getElementById('import-data-btn');
        importDataBtn.addEventListener('click', () => {
            this.importAccountData();
        });

        // 清空数据按钮
        const clearDataBtn = document.getElementById('clear-data-btn');
        clearDataBtn.addEventListener('click', () => {
            this.clearAccountData();
        });

        // 文件输入
        const importFileInput = document.getElementById('import-file-input');
        importFileInput.addEventListener('change', (e) => {
            this.handleFileImport(e);
        });

        // 返回按钮
        const backFromAccountBtn = document.getElementById('back-from-account-btn');
        backFromAccountBtn.addEventListener('click', () => {
            this.switchView('main-menu');
        });
    }

    /**
     * 设置全局事件
     */
    setupGlobalEvents() {
        // 级别解锁事件
        window.addEventListener('levelUnlocked', (e) => {
            this.handleLevelUnlock(e.detail.level);
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && exerciseManager.isActive) {
                exerciseManager.pauseSession();
            } else if (!document.hidden && exerciseManager.currentSession) {
                exerciseManager.resumeSession();
            }
        });
    }

    /**
     * 视图切换
     */
    switchView(viewName) {
        if (!this.views[viewName]) {
            console.error(`未找到视图: ${viewName}`);
            return;
        }

        // 隐藏所有视图
        Object.values(this.views).forEach(view => {
            view.classList.remove('active');
        });

        // 显示目标视图
        this.views[viewName].classList.add('active');
        this.currentView = viewName;

        // 视图切换后的处理
        this.onViewChanged(viewName);
    }

    /**
     * 视图切换后的处理
     */
    onViewChanged(viewName) {
        switch (viewName) {
            case 'main-menu':
                this.updateMainMenu();
                break;
            case 'exercise-view':
                this.focusAnswerInput();
                this.ensureVirtualKeyboard();
                break;
            case 'wrong-questions-view':
                this.updateWrongQuestionsList();
                break;
            case 'progress-view':
                this.updateProgressView();
                break;
            case 'account-view':
                this.updateAccountView();
                break;
        }
    }

    /**
     * 确保虚拟键盘存在
     */
    ensureVirtualKeyboard() {
        const existingKeyboard = document.querySelector('.virtual-keyboard');
        if (!existingKeyboard) {
            this.createVirtualKeyboard();
        }
        
        // 根据设备类型显示/隐藏虚拟键盘
        const keyboard = document.querySelector('.virtual-keyboard');
        if (keyboard) {
            keyboard.style.display = this.isMobileDevice() ? 'block' : 'none';
        }
    }

    /**
     * 更新主菜单
     */
    updateMainMenu() {
        const user = storageManager.getUser();
        if (!user) return;

        const userProfile = new UserProfile(user);
        
        // 更新当前用户名显示
        const currentUserNameElement = document.getElementById('current-user-name');
        if (currentUserNameElement) {
            currentUserNameElement.textContent = userProfile.name;
        }

        const levelButtons = document.querySelectorAll('.level-btn');

        levelButtons.forEach(btn => {
            const level = btn.dataset.level;
            const isUnlocked = userProfile.unlockedLevels.includes(level);
            
            btn.disabled = !isUnlocked;
            btn.classList.toggle('disabled', !isUnlocked);
            
            if (!isUnlocked) {
                btn.textContent += ' (未解锁)';
            }
        });

        // 更新错题复训按钮状态
        const wrongQuestions = storageManager.getWrongQuestions();
        const wrongQuestionsBtn = document.getElementById('wrong-questions-btn');
        const activeWrongQuestions = wrongQuestions.filter(wq => !wq.masteredAt);
        
        if (activeWrongQuestions.length === 0) {
            wrongQuestionsBtn.disabled = true;
            wrongQuestionsBtn.textContent = '错题复训 (暂无错题)';
        } else {
            wrongQuestionsBtn.disabled = false;
            wrongQuestionsBtn.textContent = `错题复训 (${activeWrongQuestions.length}题)`;
        }
    }

    /**
     * 开始练习
     */
    startExercise(level) {
        try {
            // 获取用户设置的题目数量
            const questionCountSelect = document.getElementById('question-count');
            const questionCount = parseInt(questionCountSelect.value) || 50;

            const session = exerciseManager.startSession(level, questionCount);
            this.exerciseStartTime = new Date();

            this.switchView('exercise-view');
            this.updateExerciseView();
            this.startTimer();

            console.log(`开始${level}练习，共${questionCount}道题`);
        } catch (error) {
            console.error('开始练习失败:', error);
            this.showError('开始练习失败，请重试');
        }
    }

    /**
     * 开始错题复训
     */
    startWrongQuestionReview() {
        try {
            const wrongQuestions = storageManager.getWrongQuestions();
            const activeWrongQuestions = wrongQuestions.filter(wq => !wq.masteredAt);
            
            if (activeWrongQuestions.length === 0) {
                this.showMessage('暂无需要复训的错题');
                return;
            }

            const session = exerciseManager.startReviewSession(activeWrongQuestions, 20);
            this.exerciseStartTime = new Date();
            
            this.switchView('exercise-view');
            this.updateExerciseView();
            this.startTimer();
            
            console.log('开始错题复训');
        } catch (error) {
            console.error('开始错题复训失败:', error);
            this.showError('开始错题复训失败，请重试');
        }
    }

    /**
     * 更新练习界面
     */
    updateExerciseView() {
        const question = exerciseManager.getCurrentQuestion();
        if (!question) {
            this.completeExercise();
            return;
        }

        // 更新题目显示
        const questionText = document.querySelector('.question-text');
        questionText.textContent = `${question.getExpression()} = ?`;

        // 更新进度
        const progress = exerciseManager.getProgress();
        const progressFill = document.querySelector('.progress-fill');
        const progressText = document.querySelector('.progress-text');
        
        progressFill.style.width = `${progress.percentage}%`;
        progressText.textContent = `${progress.current}/${progress.total}`;

        // 清空答案输入框
        const answerInput = document.getElementById('answer-input');
        answerInput.value = '';
        answerInput.focus();

        // 清空反馈信息
        const feedbackMessage = document.getElementById('feedback-message');
        feedbackMessage.textContent = '';
        feedbackMessage.className = 'feedback-message';

        // 更新连击显示
        this.updateStreakDisplay();

        // 添加题目动画
        this.animateQuestionEntry();
    }

    /**
     * 提交答案
     */
    submitAnswer() {
        // 清除自动提交定时器
        if (this.autoSubmitTimer) {
            clearTimeout(this.autoSubmitTimer);
            this.autoSubmitTimer = null;
        }

        const answerInput = document.getElementById('answer-input');
        const userAnswer = answerInput.value.trim();

        if (userAnswer === '') {
            this.showFeedback('请输入答案', false);
            return;
        }

        try {
            const result = exerciseManager.checkAnswer(parseFloat(userAnswer));
            this.showFeedback(result);

            // 根据答案正确性调整延迟时间
            const delay = result.isCorrect ? 800 : 1500;

            // 延迟显示下一题
            setTimeout(() => {
                if (result.isLastQuestion) {
                    this.completeExercise();
                } else {
                    this.updateExerciseView();
                }
            }, delay);

        } catch (error) {
            console.error('提交答案失败:', error);
            this.showFeedback('提交答案失败，请重试', false);
        }
    }

    /**
     * 显示答题反馈
     */
    showFeedback(result) {
        const feedbackMessage = document.getElementById('feedback-message');
        
        if (typeof result === 'object') {
            if (result.isCorrect) {
                feedbackMessage.textContent = '正确！';
                feedbackMessage.className = 'feedback-message correct';
            } else {
                feedbackMessage.textContent = `错误！正确答案是 ${result.correctAnswer}`;
                feedbackMessage.className = 'feedback-message incorrect';
            }
        } else {
            feedbackMessage.textContent = result;
            feedbackMessage.className = 'feedback-message incorrect';
        }
    }

    /**
     * 完成练习
     */
    completeExercise() {
        this.stopTimer();
        
        const result = exerciseManager.completeSession();
        if (!result) {
            this.showError('完成练习失败');
            return;
        }

        // 记录到进度跟踪器
        const sessionData = exerciseManager.currentSession;
        if (sessionData) {
            progressTracker.recordSession(sessionData.toJSON());
        }

        // 计算奖励
        const user = storageManager.getUser();
        if (user && sessionData) {
            const pointsResult = rewardSystem.calculatePoints(sessionData.toJSON());
            const achievements = rewardSystem.checkAchievements(sessionData.toJSON(), user);
            
            // 显示新成就
            achievements.forEach(achievement => {
                setTimeout(() => {
                    rewardSystem.displayCelebration('new_achievement', {
                        achievementName: achievement.name
                    });
                }, 1000);
            });
        }

        this.showResult(result);
    }

    /**
     * 显示结果
     */
    showResult(result) {
        // 更新结果数据
        document.getElementById('accuracy-rate').textContent = `${result.accuracy}%`;
        document.getElementById('time-spent').textContent = this.formatTime(result.totalTime);
        document.getElementById('points-earned').textContent = result.score;

        // 添加详细统计信息
        this.updateDetailedResults(result);

        // 更新奖励展示
        this.updateRewardDisplay(result);

        // 切换到结果视图
        this.switchView('result-view');

        // 显示庆祝动画（如果成绩优秀）
        if (result.accuracy >= 90) {
            this.showCelebration();
        }

        // 检查并显示成就
        this.checkAndShowAchievements(result);
    }

    /**
     * 更新详细结果信息
     */
    updateDetailedResults(result) {
        // 创建详细结果容器（如果不存在）
        let detailsContainer = document.querySelector('.result-details');
        if (!detailsContainer) {
            detailsContainer = document.createElement('div');
            detailsContainer.className = 'result-details';
            
            const resultStats = document.querySelector('.result-stats');
            resultStats.parentNode.insertBefore(detailsContainer, resultStats.nextSibling);
        }

        // 获取答题历史
        const answerHistory = exerciseManager.getAnswerHistory();
        const wrongAnswers = answerHistory.filter(answer => !answer.isCorrect);

        // 计算平均答题时间
        const avgTime = answerHistory.length > 0 ? 
            Math.round(answerHistory.reduce((sum, answer) => sum + answer.timeSpent, 0) / answerHistory.length) : 0;

        // 计算最快和最慢答题时间
        const times = answerHistory.map(answer => answer.timeSpent).filter(time => time > 0);
        const fastestTime = times.length > 0 ? Math.min(...times) : 0;
        const slowestTime = times.length > 0 ? Math.max(...times) : 0;

        detailsContainer.innerHTML = `
            <div class="detail-stats">
                <div class="detail-item">
                    <span class="detail-label">平均用时</span>
                    <span class="detail-value">${avgTime}秒</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">最快用时</span>
                    <span class="detail-value">${fastestTime}秒</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">最慢用时</span>
                    <span class="detail-value">${slowestTime}秒</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">错题数量</span>
                    <span class="detail-value">${wrongAnswers.length}题</span>
                </div>
            </div>
            ${wrongAnswers.length > 0 ? this.generateWrongAnswersSummary(wrongAnswers) : ''}
        `;
    }

    /**
     * 生成错题摘要
     */
    generateWrongAnswersSummary(wrongAnswers) {
        const summaryHTML = wrongAnswers.slice(0, 5).map(answer => `
            <div class="wrong-answer-item">
                <span class="wrong-question">${answer.question} = ${answer.correctAnswer}</span>
                <span class="wrong-user-answer">你的答案: ${answer.userAnswer || '未答'}</span>
            </div>
        `).join('');

        return `
            <div class="wrong-answers-summary">
                <h4>错题回顾</h4>
                <div class="wrong-answers-list">
                    ${summaryHTML}
                    ${wrongAnswers.length > 5 ? `<div class="more-wrong-answers">还有 ${wrongAnswers.length - 5} 道错题...</div>` : ''}
                </div>
            </div>
        `;
    }

    /**
     * 更新奖励展示
     */
    updateRewardDisplay(result) {
        let rewardContainer = document.querySelector('.reward-display');
        if (!rewardContainer) {
            rewardContainer = document.createElement('div');
            rewardContainer.className = 'reward-display';
            
            const resultActions = document.querySelector('.result-actions');
            resultActions.parentNode.insertBefore(rewardContainer, resultActions);
        }

        const rewards = this.calculateRewards(result);
        
        if (rewards.length > 0) {
            const rewardsHTML = rewards.map(reward => `
                <div class="reward-item ${reward.type}">
                    <div class="reward-icon">${reward.icon}</div>
                    <div class="reward-text">
                        <div class="reward-name">${reward.name}</div>
                        <div class="reward-description">${reward.description}</div>
                        <div class="reward-points">+${reward.points}分</div>
                    </div>
                </div>
            `).join('');

            rewardContainer.innerHTML = `
                <h3>获得奖励</h3>
                <div class="rewards-list">
                    ${rewardsHTML}
                </div>
            `;
        } else {
            rewardContainer.innerHTML = '';
        }
    }

    /**
     * 计算奖励
     */
    calculateRewards(result) {
        const rewards = [];

        // 正确率奖励
        if (result.accuracy === 100) {
            rewards.push({
                type: 'perfect',
                icon: '💯',
                name: '完美表现',
                description: '全部答对！',
                points: 100
            });
        } else if (result.accuracy >= 90) {
            rewards.push({
                type: 'excellent',
                icon: '⭐',
                name: '优秀表现',
                description: '正确率超过90%',
                points: 50
            });
        } else if (result.accuracy >= 80) {
            rewards.push({
                type: 'good',
                icon: '👍',
                name: '良好表现',
                description: '正确率超过80%',
                points: 30
            });
        }

        // 速度奖励
        const avgTime = result.totalTime / result.totalCount;
        if (avgTime <= 5) {
            rewards.push({
                type: 'speed',
                icon: '⚡',
                name: '闪电速度',
                description: '平均5秒内完成',
                points: 50
            });
        } else if (avgTime <= 10) {
            rewards.push({
                type: 'speed',
                icon: '🚀',
                name: '快速反应',
                description: '平均10秒内完成',
                points: 30
            });
        }

        // 连击奖励
        const maxStreak = this.calculateMaxStreak();
        if (maxStreak >= 10) {
            rewards.push({
                type: 'streak',
                icon: '🔥',
                name: '连击高手',
                description: `最高连击${maxStreak}次`,
                points: maxStreak * 2
            });
        }

        return rewards;
    }

    /**
     * 计算最大连击数
     */
    calculateMaxStreak() {
        const answerHistory = exerciseManager.getAnswerHistory();
        let maxStreak = 0;
        let currentStreak = 0;

        answerHistory.forEach(answer => {
            if (answer.isCorrect) {
                currentStreak++;
                maxStreak = Math.max(maxStreak, currentStreak);
            } else {
                currentStreak = 0;
            }
        });

        return maxStreak;
    }

    /**
     * 检查并显示成就
     */
    checkAndShowAchievements(result) {
        const user = storageManager.getUser();
        if (!user) return;

        const userProfile = new UserProfile(user);
        const newAchievements = [];

        // 检查各种成就条件
        if (result.accuracy === 100 && !userProfile.achievements.includes('perfectionist')) {
            newAchievements.push('perfectionist');
        }

        if (result.accuracy >= 80 && !userProfile.achievements.includes('accuracy-80')) {
            newAchievements.push('accuracy-80');
        }

        const sessions = storageManager.getSessions();
        if (sessions.length === 1 && !userProfile.achievements.includes('first-session')) {
            newAchievements.push('first-session');
        }

        const avgTime = result.totalTime / result.totalCount;
        if (avgTime <= 5 && !userProfile.achievements.includes('speed-demon')) {
            newAchievements.push('speed-demon');
        }

        const streakDays = this.calculateStreakDays(sessions);
        if (streakDays >= 7 && !userProfile.achievements.includes('persistent')) {
            newAchievements.push('persistent');
        }

        // 保存新成就
        if (newAchievements.length > 0) {
            userProfile.achievements.push(...newAchievements);
            storageManager.setUser(userProfile.toJSON());
            
            // 显示成就通知
            this.showAchievementNotification(newAchievements);
        }
    }

    /**
     * 显示成就通知
     */
    showAchievementNotification(achievements) {
        const achievementNames = {
            'perfectionist': { name: '完美主义者', icon: '💯' },
            'accuracy-80': { name: '准确射手', icon: '🎯' },
            'first-session': { name: '初次尝试', icon: '🌟' },
            'speed-demon': { name: '速度之王', icon: '⚡' },
            'persistent': { name: '坚持不懈', icon: '💪' }
        };

        achievements.forEach(achievementId => {
            const achievement = achievementNames[achievementId];
            if (achievement) {
                setTimeout(() => {
                    this.showMessage(`🎉 解锁成就：${achievement.icon} ${achievement.name}`, 'success');
                }, 1000);
            }
        });
    }

    /**
     * 显示错题本
     */
    showWrongQuestions() {
        this.switchView('wrong-questions-view');
    }

    /**
     * 更新错题列表
     */
    updateWrongQuestionsList() {
        const wrongQuestions = storageManager.getWrongQuestions();
        const activeWrongQuestions = wrongQuestions.filter(wq => !wq.masteredAt);
        const masteredQuestions = wrongQuestions.filter(wq => wq.masteredAt);
        const listContainer = document.getElementById('wrong-questions-list');

        if (activeWrongQuestions.length === 0 && masteredQuestions.length === 0) {
            listContainer.innerHTML = '<p style="text-align: center; color: #666;">暂无错题，继续加油！</p>';
            this.updateWrongQuestionsActions(0);
            return;
        }

        // 创建筛选和统计区域
        const filterHTML = this.createWrongQuestionsFilter(activeWrongQuestions, masteredQuestions);
        
        // 创建错题列表
        const listHTML = this.createWrongQuestionsList(activeWrongQuestions, masteredQuestions);

        listContainer.innerHTML = filterHTML + listHTML;

        // 更新操作按钮状态
        this.updateWrongQuestionsActions(activeWrongQuestions.length);

        // 设置筛选事件
        this.setupWrongQuestionsFilter();
    }

    /**
     * 创建错题筛选器
     */
    createWrongQuestionsFilter(activeQuestions, masteredQuestions) {
        return `
            <div class="wrong-questions-header">
                <div class="wrong-questions-stats">
                    <div class="stat-badge active">
                        <span class="badge-number">${activeQuestions.length}</span>
                        <span class="badge-label">待复习</span>
                    </div>
                    <div class="stat-badge mastered">
                        <span class="badge-number">${masteredQuestions.length}</span>
                        <span class="badge-label">已掌握</span>
                    </div>
                </div>
                <div class="wrong-questions-filter">
                    <button class="filter-btn active" data-filter="active">待复习</button>
                    <button class="filter-btn" data-filter="mastered">已掌握</button>
                    <button class="filter-btn" data-filter="all">全部</button>
                </div>
                <div class="wrong-questions-sort">
                    <select id="sort-wrong-questions">
                        <option value="recent">最近错误</option>
                        <option value="frequency">错误次数</option>
                        <option value="difficulty">难度</option>
                        <option value="type">题目类型</option>
                    </select>
                </div>
            </div>
        `;
    }

    /**
     * 创建错题列表
     */
    createWrongQuestionsList(activeQuestions, masteredQuestions) {
        const allQuestions = [...activeQuestions, ...masteredQuestions];
        
        if (allQuestions.length === 0) {
            return '<div class="empty-state">暂无错题数据</div>';
        }

        const listHTML = allQuestions.map(wq => {
            const question = new Question(wq.question);
            const isMastered = wq.masteredAt;
            const difficultyText = this.getDifficultyText(question.difficulty);
            const typeText = this.getTypeText(question.type);
            
            return `
                <div class="wrong-question-item ${isMastered ? 'mastered' : 'active'}" 
                     data-type="${question.type}" 
                     data-difficulty="${question.difficulty}"
                     data-category="${question.category}">
                    <div class="question-main">
                        <div class="question-expression">
                            ${question.getExpression()} = ${question.correctAnswer}
                        </div>
                        <div class="question-meta">
                            <span class="question-type">${typeText}</span>
                            <span class="question-difficulty">${difficultyText}</span>
                            <span class="question-category">${this.getCategoryText(question.category)}</span>
                        </div>
                    </div>
                    <div class="question-stats">
                        <div class="stat-row">
                            <span class="stat-label">你的答案:</span>
                            <span class="stat-value ${wq.userAnswer === null ? 'skipped' : 'wrong'}">${wq.userAnswer || '跳过'}</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">错误次数:</span>
                            <span class="stat-value">${wq.wrongCount}</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">最后错误:</span>
                            <span class="stat-value">${new Date(wq.lastWrongAt).toLocaleDateString()}</span>
                        </div>
                        ${isMastered ? `
                            <div class="stat-row">
                                <span class="stat-label">掌握时间:</span>
                                <span class="stat-value mastered-time">${new Date(wq.masteredAt).toLocaleDateString()}</span>
                            </div>
                        ` : `
                            <div class="stat-row">
                                <span class="stat-label">连续正确:</span>
                                <span class="stat-value">${wq.correctStreak || 0}/3</span>
                            </div>
                        `}
                    </div>
                    <div class="question-actions">
                        ${!isMastered ? `
                            <button class="practice-single-btn" data-question-id="${wq.id}">单独练习</button>
                            <button class="mark-mastered-btn" data-question-id="${wq.id}">标记掌握</button>
                        ` : `
                            <span class="mastered-badge">✓ 已掌握</span>
                        `}
                        <button class="delete-question-btn" data-question-id="${wq.id}">删除</button>
                    </div>
                </div>
            `;
        }).join('');

        return `<div class="wrong-questions-container">${listHTML}</div>`;
    }

    /**
     * 获取难度文本
     */
    getDifficultyText(difficulty) {
        const difficultyMap = {
            1: '简单',
            2: '中等',
            3: '困难'
        };
        return difficultyMap[difficulty] || '未知';
    }

    /**
     * 获取类型文本
     */
    getTypeText(type) {
        const typeMap = {
            'addition': '加法',
            'subtraction': '减法',
            'multiplication': '乘法'
        };
        return typeMap[type] || '未知';
    }

    /**
     * 获取分类文本
     */
    getCategoryText(category) {
        const categoryMap = {
            'within-10': '10以内',
            'within-20': '20以内',
            'within-100': '100以内',
            'multiplication': '乘法表'
        };
        return categoryMap[category] || '未知';
    }

    /**
     * 更新错题操作按钮
     */
    updateWrongQuestionsActions(activeCount) {
        const startReviewBtn = document.getElementById('start-review-btn');
        
        if (activeCount === 0) {
            startReviewBtn.disabled = true;
            startReviewBtn.textContent = '暂无错题需要复训';
        } else {
            startReviewBtn.disabled = false;
            startReviewBtn.textContent = `开始复训 (${activeCount}题)`;
        }
    }

    /**
     * 设置错题筛选器事件
     */
    setupWrongQuestionsFilter() {
        // 筛选按钮事件
        const filterBtns = document.querySelectorAll('.filter-btn');
        filterBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                // 更新按钮状态
                filterBtns.forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                
                // 应用筛选
                this.applyWrongQuestionsFilter(e.target.dataset.filter);
            });
        });

        // 排序选择器事件
        const sortSelect = document.getElementById('sort-wrong-questions');
        sortSelect.addEventListener('change', (e) => {
            this.sortWrongQuestions(e.target.value);
        });

        // 单独练习按钮事件
        const practiceBtns = document.querySelectorAll('.practice-single-btn');
        practiceBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const questionId = e.target.dataset.questionId;
                this.startSingleQuestionPractice(questionId);
            });
        });

        // 标记掌握按钮事件
        const markMasteredBtns = document.querySelectorAll('.mark-mastered-btn');
        markMasteredBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const questionId = e.target.dataset.questionId;
                this.markQuestionAsMastered(questionId);
            });
        });

        // 删除按钮事件
        const deleteBtns = document.querySelectorAll('.delete-question-btn');
        deleteBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const questionId = e.target.dataset.questionId;
                this.deleteWrongQuestion(questionId);
            });
        });
    }

    /**
     * 应用错题筛选
     */
    applyWrongQuestionsFilter(filter) {
        const questionItems = document.querySelectorAll('.wrong-question-item');
        
        questionItems.forEach(item => {
            const isMastered = item.classList.contains('mastered');
            let shouldShow = false;
            
            switch (filter) {
                case 'active':
                    shouldShow = !isMastered;
                    break;
                case 'mastered':
                    shouldShow = isMastered;
                    break;
                case 'all':
                    shouldShow = true;
                    break;
            }
            
            item.style.display = shouldShow ? 'block' : 'none';
        });
    }

    /**
     * 排序错题
     */
    sortWrongQuestions(sortBy) {
        const container = document.querySelector('.wrong-questions-container');
        const items = Array.from(container.querySelectorAll('.wrong-question-item'));
        
        items.sort((a, b) => {
            switch (sortBy) {
                case 'recent':
                    // 按最后错误时间排序
                    const timeA = new Date(a.querySelector('.stat-value:nth-of-type(3)').textContent);
                    const timeB = new Date(b.querySelector('.stat-value:nth-of-type(3)').textContent);
                    return timeB - timeA;
                case 'frequency':
                    // 按错误次数排序
                    const freqA = parseInt(a.querySelector('.stat-value:nth-of-type(2)').textContent);
                    const freqB = parseInt(b.querySelector('.stat-value:nth-of-type(2)').textContent);
                    return freqB - freqA;
                case 'difficulty':
                    // 按难度排序
                    const diffA = parseInt(a.dataset.difficulty);
                    const diffB = parseInt(b.dataset.difficulty);
                    return diffB - diffA;
                case 'type':
                    // 按类型排序
                    const typeA = a.dataset.type;
                    const typeB = b.dataset.type;
                    return typeA.localeCompare(typeB);
            }
        });
        
        // 重新排列DOM元素
        items.forEach(item => container.appendChild(item));
    }

    /**
     * 开始单个题目练习
     */
    startSingleQuestionPractice(questionId) {
        const wrongQuestions = storageManager.getWrongQuestions();
        const wrongQuestion = wrongQuestions.find(wq => wq.id === questionId);
        
        if (!wrongQuestion) {
            this.showError('未找到指定错题');
            return;
        }

        try {
            // 创建包含该错题和相似题目的小型练习
            const practiceQuestions = [
                new Question(wrongQuestion.question),
                ...questionBankManager.generateReviewQuestions([wrongQuestion], 4)
            ];

            // 开始练习会话
            const user = storageManager.getUser();
            exerciseManager.currentSession = new ExerciseSession({
                userId: user ? user.id : 'anonymous',
                level: 'single-review',
                questionBank: 'single-question',
                questions: practiceQuestions.map(q => q.toJSON()),
                startTime: new Date()
            });

            exerciseManager.currentQuestionIndex = 0;
            exerciseManager.startTime = new Date();
            exerciseManager.isActive = true;
            exerciseManager.questionStartTime = new Date();

            this.switchView('exercise-view');
            this.updateExerciseView();
            this.startTimer();
            
            console.log('开始单题练习:', wrongQuestion.question.getExpression());
        } catch (error) {
            console.error('开始单题练习失败:', error);
            this.showError('开始练习失败，请重试');
        }
    }

    /**
     * 标记题目为已掌握
     */
    markQuestionAsMastered(questionId) {
        if (confirm('确定要标记这道题为已掌握吗？')) {
            const wrongQuestions = storageManager.getWrongQuestions();
            const questionIndex = wrongQuestions.findIndex(wq => wq.id === questionId);
            
            if (questionIndex >= 0) {
                wrongQuestions[questionIndex].masteredAt = new Date();
                wrongQuestions[questionIndex].correctStreak = 3;
                storageManager.setWrongQuestions(wrongQuestions);
                
                this.updateWrongQuestionsList();
                this.showMessage('已标记为掌握', 'success');
            }
        }
    }

    /**
     * 删除错题
     */
    deleteWrongQuestion(questionId) {
        if (confirm('确定要删除这道错题吗？此操作不可撤销。')) {
            const wrongQuestions = storageManager.getWrongQuestions();
            const filteredQuestions = wrongQuestions.filter(wq => wq.id !== questionId);
            storageManager.setWrongQuestions(filteredQuestions);
            
            this.updateWrongQuestionsList();
            this.showMessage('错题已删除', 'success');
        }
    }

    /**
     * 显示进度
     */
    showProgress() {
        this.switchView('progress-view');
    }

    /**
     * 更新进度视图
     */
    updateProgressView() {
        const user = storageManager.getUser();
        if (!user) return;

        const userProfile = new UserProfile(user);
        const sessions = storageManager.getSessions();

        // 更新统计数据
        document.getElementById('total-points').textContent = userProfile.totalPoints;
        
        // 计算连续练习天数
        const streakDays = this.calculateStreakDays(sessions);
        document.getElementById('streak-days').textContent = `${streakDays}天`;

        // 更新成就列表
        this.updateAchievementsList();

        // 更新图表
        this.updateProgressChart(sessions);
    }

    /**
     * 计算连续练习天数
     */
    calculateStreakDays(sessions) {
        if (sessions.length === 0) return 0;

        const today = new Date();
        const sortedSessions = sessions.sort((a, b) => new Date(b.startTime) - new Date(a.startTime));
        
        let streakDays = 0;
        let currentDate = new Date(today);
        currentDate.setHours(0, 0, 0, 0);

        for (const session of sortedSessions) {
            const sessionDate = new Date(session.startTime);
            sessionDate.setHours(0, 0, 0, 0);

            if (sessionDate.getTime() === currentDate.getTime()) {
                streakDays++;
                currentDate.setDate(currentDate.getDate() - 1);
            } else if (sessionDate.getTime() < currentDate.getTime()) {
                break;
            }
        }

        return streakDays;
    }

    /**
     * 更新成就列表
     */
    updateAchievementsList() {
        const achievementsList = document.getElementById('achievements-list');
        const user = storageManager.getUser();
        
        // 定义成就
        const achievements = [
            { id: 'first-session', name: '初次尝试', icon: '🎯', condition: 'complete-session' },
            { id: 'accuracy-80', name: '准确射手', icon: '🎯', condition: 'accuracy-80' },
            { id: 'speed-demon', name: '速度之王', icon: '⚡', condition: 'fast-completion' },
            { id: 'persistent', name: '坚持不懈', icon: '💪', condition: 'streak-7' },
            { id: 'perfectionist', name: '完美主义', icon: '💯', condition: 'accuracy-100' }
        ];

        const achievementsHTML = achievements.map(achievement => {
            const isUnlocked = user && user.achievements && user.achievements.includes(achievement.id);
            return `
                <div class="achievement-item ${isUnlocked ? 'unlocked' : ''}">
                    <div class="achievement-icon">${achievement.icon}</div>
                    <div class="achievement-name">${achievement.name}</div>
                </div>
            `;
        }).join('');

        achievementsList.innerHTML = achievementsHTML;
    }

    /**
     * 更新进度图表
     */
    updateProgressChart(sessions) {
        const canvas = document.getElementById('progress-chart');
        const ctx = canvas.getContext('2d');

        // 准备图表数据
        const last7Days = this.getLast7DaysData(sessions);
        
        // 如果Chart.js可用，使用它创建图表
        if (typeof Chart !== 'undefined') {
            // 销毁现有图表
            if (this.progressChart) {
                this.progressChart.destroy();
            }

            this.progressChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: last7Days.map(d => d.date),
                    datasets: [{
                        label: '正确率',
                        data: last7Days.map(d => d.accuracy),
                        borderColor: '#4CAF50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        } else {
            // 简单的文本显示
            ctx.font = '16px Arial';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('图表加载中...', canvas.width / 2, canvas.height / 2);
        }
    }

    /**
     * 获取最近7天的数据
     */
    getLast7DaysData(sessions) {
        const data = [];
        const today = new Date();

        for (let i = 6; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            date.setHours(0, 0, 0, 0);

            const dayStart = new Date(date);
            const dayEnd = new Date(date);
            dayEnd.setHours(23, 59, 59, 999);

            const daySessions = sessions.filter(session => {
                const sessionDate = new Date(session.startTime);
                return sessionDate >= dayStart && sessionDate <= dayEnd;
            });

            let accuracy = 0;
            if (daySessions.length > 0) {
                const totalCorrect = daySessions.reduce((sum, s) => sum + s.correctCount, 0);
                const totalQuestions = daySessions.reduce((sum, s) => sum + s.totalCount, 0);
                accuracy = totalQuestions > 0 ? Math.round((totalCorrect / totalQuestions) * 100) : 0;
            }

            data.push({
                date: `${date.getMonth() + 1}/${date.getDate()}`,
                accuracy: accuracy
            });
        }

        return data;
    }

    /**
     * 开始计时器
     */
    startTimer() {
        this.stopTimer(); // 确保没有重复的计时器
        
        const timerElement = document.querySelector('.timer');
        
        this.currentTimer = setInterval(() => {
            if (this.exerciseStartTime) {
                const elapsed = Math.floor((new Date() - this.exerciseStartTime) / 1000);
                timerElement.textContent = this.formatTime(elapsed);
            }
        }, 1000);
    }

    /**
     * 停止计时器
     */
    stopTimer() {
        if (this.currentTimer) {
            clearInterval(this.currentTimer);
            this.currentTimer = null;
        }
    }

    /**
     * 格式化时间显示
     */
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    /**
     * 聚焦答案输入框
     */
    focusAnswerInput() {
        setTimeout(() => {
            const answerInput = document.getElementById('answer-input');
            if (answerInput) {
                answerInput.focus();
            }
        }, 100);
    }

    /**
     * 处理级别解锁
     */
    handleLevelUnlock(level) {
        const levelNames = {
            'within-20': '20以内加减法',
            'within-100': '100以内加减法',
            'within-1000': '1000以内加减法',
            'multiplication': '乘法表',
            'division': '除法练习'
        };

        // 显示级别解锁庆祝
        rewardSystem.displayCelebration('level_unlock', {
            levelName: levelNames[level] || level
        });

        this.updateMainMenu();
    }

    /**
     * 处理键盘快捷键
     */
    handleKeyboardShortcuts(e) {
        // 在练习界面按空格键跳过题目
        if (e.code === 'Space' && this.currentView === 'exercise-view') {
            e.preventDefault();
            exerciseManager.skipQuestion();
            this.updateExerciseView();
        }
        
        // ESC键返回主菜单
        if (e.code === 'Escape' && this.currentView !== 'main-menu') {
            this.switchView('main-menu');
        }
    }

    /**
     * 显示庆祝动画
     */
    showCelebration() {
        // 简单的庆祝效果
        const celebration = document.createElement('div');
        celebration.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 4rem;
            z-index: 10000;
            animation: bounce 1s ease-out;
        `;
        celebration.textContent = '🎉';
        document.body.appendChild(celebration);

        setTimeout(() => {
            celebration.remove();
        }, 2000);
    }

    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
            color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
            padding: 15px 20px;
            border-radius: 5px;
            border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
            z-index: 9999;
            max-width: 300px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        `;
        messageDiv.textContent = message;
        document.body.appendChild(messageDiv);

        setTimeout(() => {
            messageDiv.style.opacity = '0';
            messageDiv.style.transition = 'opacity 0.5s ease-out';
            setTimeout(() => messageDiv.remove(), 500);
        }, 3000);
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        this.showMessage(message, 'error');
    }

    /**
     * 更新连击显示
     */
    updateStreakDisplay() {
        const streak = exerciseManager.getCurrentStreak();
        const streakElement = document.querySelector('.streak-display');

        if (streakElement) {
            if (streak > 0) {
                streakElement.textContent = `连击 ${streak}`;
                streakElement.style.display = 'block';

                if (streak >= 5) {
                    streakElement.classList.add('streak-high');
                } else {
                    streakElement.classList.remove('streak-high');
                }
            } else {
                streakElement.style.display = 'none';
            }
        }
    }

    /**
     * 题目进入动画
     */
    animateQuestionEntry() {
        const questionArea = document.querySelector('.question-area');
        questionArea.style.transform = 'translateY(20px)';
        questionArea.style.opacity = '0.8';
        
        setTimeout(() => {
            questionArea.style.transform = 'translateY(0)';
            questionArea.style.opacity = '1';
            questionArea.style.transition = 'all 0.3s ease-out';
        }, 50);
    }

    /**
     * 添加触摸支持
     */
    setupTouchSupport() {
        const answerInput = document.getElementById('answer-input');
        
        // 数字键盘支持
        if (answerInput) {
            answerInput.setAttribute('inputmode', 'numeric');
            answerInput.setAttribute('pattern', '[0-9]*');
        }

        // 添加虚拟数字键盘
        this.createVirtualKeyboard();
    }

    /**
     * 创建虚拟数字键盘
     */
    createVirtualKeyboard() {
        const keyboardContainer = document.createElement('div');
        keyboardContainer.className = 'virtual-keyboard';
        keyboardContainer.innerHTML = `
            <div class="keyboard-row">
                <button class="key-btn" data-key="7">7</button>
                <button class="key-btn" data-key="8">8</button>
                <button class="key-btn" data-key="9">9</button>
            </div>
            <div class="keyboard-row">
                <button class="key-btn" data-key="4">4</button>
                <button class="key-btn" data-key="5">5</button>
                <button class="key-btn" data-key="6">6</button>
            </div>
            <div class="keyboard-row">
                <button class="key-btn" data-key="1">1</button>
                <button class="key-btn" data-key="2">2</button>
                <button class="key-btn" data-key="3">3</button>
            </div>
            <div class="keyboard-row">
                <button class="key-btn" data-key="0">0</button>
                <button class="key-btn key-backspace" data-key="backspace">⌫</button>
                <button class="key-btn key-enter" data-key="enter">确定</button>
            </div>
        `;

        // 添加键盘事件
        keyboardContainer.addEventListener('click', (e) => {
            if (e.target.classList.contains('key-btn')) {
                this.handleVirtualKeyPress(e.target.dataset.key);
            }
        });

        // 插入到练习界面
        const exerciseView = document.getElementById('exercise-view');
        const container = exerciseView.querySelector('.container');
        container.appendChild(keyboardContainer);

        // 检测移动设备，决定是否显示虚拟键盘
        if (this.isMobileDevice()) {
            keyboardContainer.style.display = 'block';
        } else {
            keyboardContainer.style.display = 'none';
        }
    }

    /**
     * 处理虚拟键盘按键
     */
    handleVirtualKeyPress(key) {
        const answerInput = document.getElementById('answer-input');
        
        if (key === 'backspace') {
            answerInput.value = answerInput.value.slice(0, -1);
        } else if (key === 'enter') {
            this.submitAnswer();
        } else {
            answerInput.value += key;
        }
        
        answerInput.focus();
    }

    /**
     * 检测是否为移动设备
     */
    isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               window.innerWidth <= 768;
    }

    /**
     * 添加提示功能
     */
    showHint() {
        const hint = exerciseManager.getHint();
        if (hint) {
            this.showMessage(hint, 'info');
        }
    }

    /**
     * 添加跳过功能
     */
    skipCurrentQuestion() {
        const result = exerciseManager.skipQuestion();
        this.showFeedback('已跳过，正确答案是 ' + result.correctAnswer, false);
        
        setTimeout(() => {
            if (result.isLastQuestion) {
                this.completeExercise();
            } else {
                this.updateExerciseView();
            }
        }, 1500);
    }

    /**
     * 验证输入
     */
    validateInput(input) {
        // 只允许数字和负号
        let value = input.value;
        value = value.replace(/[^0-9-]/g, '');
        
        // 确保负号只能在开头
        if (value.indexOf('-') > 0) {
            value = value.replace(/-/g, '');
        }
        
        // 限制长度
        if (value.length > 4) {
            value = value.substring(0, 4);
        }
        
        input.value = value;
    }

    /**
     * 检查是否自动提交答案
     */
    checkAutoSubmit(input) {
        const userAnswer = input.value.trim();
        
        // 如果输入为空，不自动提交
        if (userAnswer === '') return;
        
        // 获取当前题目
        const question = exerciseManager.getCurrentQuestion();
        if (!question) return;
        
        // 检查答案是否正确
        const isCorrect = parseFloat(userAnswer) === question.correctAnswer;
        
        // 如果答案正确，延迟500ms自动提交
        if (isCorrect) {
            // 清除之前的自动提交定时器
            if (this.autoSubmitTimer) {
                clearTimeout(this.autoSubmitTimer);
            }
            
            this.autoSubmitTimer = setTimeout(() => {
                this.submitAnswer();
            }, 500);
        } else {
            // 如果答案错误，清除自动提交定时器
            if (this.autoSubmitTimer) {
                clearTimeout(this.autoSubmitTimer);
                this.autoSubmitTimer = null;
            }
        }
    }

    /**
     * 增强的反馈显示
     */
    showEnhancedFeedback(result) {
        const feedbackMessage = document.getElementById('feedback-message');
        
        if (result.isCorrect) {
            const encouragements = ['太棒了！', '正确！', '很好！', '继续加油！', '完美！'];
            const randomEncouragement = encouragements[Math.floor(Math.random() * encouragements.length)];
            
            feedbackMessage.textContent = randomEncouragement;
            feedbackMessage.className = 'feedback-message correct';
            
            // 添加正确答案的特效
            this.addCorrectAnswerEffect();
        } else {
            feedbackMessage.textContent = `答案是 ${result.correctAnswer}，再接再厉！`;
            feedbackMessage.className = 'feedback-message incorrect';
            
            // 添加错误答案的提示
            this.addIncorrectAnswerHint(result);
        }
    }

    /**
     * 正确答案特效
     */
    addCorrectAnswerEffect() {
        const questionArea = document.querySelector('.question-area');
        questionArea.style.background = 'linear-gradient(45deg, #d4edda, #c3e6cb)';
        
        setTimeout(() => {
            questionArea.style.background = 'white';
            questionArea.style.transition = 'background 0.5s ease-out';
        }, 1000);
    }

    /**
     * 错误答案提示
     */
    addIncorrectAnswerHint(result) {
        const currentQuestion = exerciseManager.getCurrentQuestion();
        if (!currentQuestion) return;
        
        const questionArea = document.querySelector('.question-area');
        questionArea.style.background = 'linear-gradient(45deg, #f8d7da, #f5c6cb)';
        
        setTimeout(() => {
            questionArea.style.background = 'white';
            questionArea.style.transition = 'background 0.5s ease-out';
        }, 1000);
    }

    /**
     * 显示账号设置
     */
    showAccountSettings() {
        this.switchView('account-view');
    }

    /**
     * 更新账号设置界面
     */
    updateAccountView() {
        const currentAccount = accountManager.getCurrentAccount();
        if (!currentAccount) return;

        // 更新当前账号信息
        const accountNameElement = document.getElementById('current-account-name');
        const accountStatsElement = document.getElementById('current-account-stats');
        
        if (accountNameElement) {
            accountNameElement.textContent = currentAccount.name;
        }
        
        if (accountStatsElement) {
            const stats = accountManager.getAccountStats();
            accountStatsElement.textContent = `积分: ${stats.totalPoints} | 练习: ${stats.totalSessions}次`;
        }

        // 更新账号列表
        this.updateAccountsList();
    }

    /**
     * 更新账号列表
     */
    updateAccountsList() {
        const accountsList = document.getElementById('accounts-list');
        const accounts = accountManager.getAllAccounts();

        if (accounts.length === 0) {
            accountsList.innerHTML = '<p style="text-align: center; color: #666;">暂无账号</p>';
            return;
        }

        const accountsHTML = accounts.map(account => `
            <div class="account-item ${account.isActive ? 'active' : ''}">
                <div class="account-item-info">
                    <div class="account-item-name">${account.name}</div>
                    <div class="account-item-stats">
                        积分: ${account.totalPoints} | 练习: ${account.sessionsCount}次 | 成就: ${account.achievementsCount}个
                    </div>
                </div>
                <div class="account-item-actions">
                    ${!account.isActive ? `<button class="switch-btn" data-account-id="${account.id}">切换</button>` : '<span style="color: #007bff; font-weight: bold;">当前</span>'}
                    ${accounts.length > 1 && !account.isActive ? `<button class="delete-btn" data-account-id="${account.id}">删除</button>` : ''}
                </div>
            </div>
        `).join('');

        accountsList.innerHTML = accountsHTML;

        // 添加事件监听器
        accountsList.querySelectorAll('.switch-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const accountId = e.target.dataset.accountId;
                this.switchToAccount(accountId);
            });
        });

        accountsList.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const accountId = e.target.dataset.accountId;
                this.deleteAccount(accountId);
            });
        });
    }

    /**
     * 编辑当前账号
     */
    editCurrentAccount() {
        const currentAccount = accountManager.getCurrentAccount();
        if (!currentAccount) return;

        this.showModal('编辑账号', '请输入新的账号名称:', currentAccount.name, (newName) => {
            if (newName && newName.trim() !== '') {
                try {
                    accountManager.renameAccount(currentAccount.id, newName.trim());
                    this.updateAccountView();
                    this.showMessage('账号名称修改成功', 'success');
                } catch (error) {
                    this.showError('修改失败: ' + error.message);
                }
            }
        });
    }

    /**
     * 创建新账号
     */
    createNewAccount() {
        this.showModal('创建账号', '请输入账号名称:', '', (name) => {
            if (name && name.trim() !== '') {
                try {
                    if (!accountManager.isAccountNameAvailable(name.trim())) {
                        this.showError('账号名称已存在');
                        return;
                    }
                    
                    const accountId = accountManager.createAccount(name.trim());
                    this.updateAccountView();
                    this.showMessage('账号创建成功', 'success');
                } catch (error) {
                    this.showError('创建失败: ' + error.message);
                }
            }
        });
    }

    /**
     * 显示账号切换器
     */
    showAccountSwitcher() {
        const accounts = accountManager.getAllAccounts();
        const inactiveAccounts = accounts.filter(account => !account.isActive);
        
        if (inactiveAccounts.length === 0) {
            this.showMessage('没有其他账号可切换');
            return;
        }

        // 创建账号选择模态框
        const modal = document.createElement('div');
        modal.className = 'modal';
        
        const accountOptions = inactiveAccounts.map(account => `
            <div class="account-option" data-account-id="${account.id}" style="
                padding: 1rem;
                margin: 0.5rem 0;
                background: #f8f9fa;
                border-radius: 8px;
                cursor: pointer;
                border: 2px solid transparent;
                transition: all 0.3s ease;
            " onmouseover="this.style.borderColor='#007bff'" onmouseout="this.style.borderColor='transparent'">
                <div style="font-weight: bold;">${account.name}</div>
                <div style="font-size: 0.9rem; color: #666;">积分: ${account.totalPoints} | 练习: ${account.sessionsCount}次</div>
            </div>
        `).join('');

        modal.innerHTML = `
            <div class="modal-content">
                <h3>选择要切换的账号</h3>
                <div class="account-options">
                    ${accountOptions}
                </div>
                <div class="modal-actions">
                    <button class="secondary" onclick="this.closest('.modal').remove()">取消</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 添加点击事件
        modal.querySelectorAll('.account-option').forEach(option => {
            option.addEventListener('click', (e) => {
                const accountId = e.currentTarget.dataset.accountId;
                this.switchToAccount(accountId);
                modal.remove();
            });
        });
    }

    /**
     * 切换到指定账号
     */
    switchToAccount(accountId) {
        try {
            accountManager.switchAccount(accountId);
            this.updateAccountView();
            this.updateMainMenu(); // 更新主菜单以反映新账号状态
            this.showMessage('账号切换成功', 'success');
        } catch (error) {
            this.showError('切换失败: ' + error.message);
        }
    }

    /**
     * 删除当前账号
     */
    deleteCurrentAccount() {
        const accounts = accountManager.getAllAccounts();
        if (accounts.length <= 1) {
            this.showError('至少需要保留一个账号');
            return;
        }

        if (confirm('确定要删除当前账号吗？此操作不可撤销，所有数据将被永久删除。')) {
            try {
                // 先切换到其他账号
                const otherAccount = accounts.find(account => !account.isActive);
                if (otherAccount) {
                    const currentAccountId = accountManager.currentAccountId;
                    accountManager.switchAccount(otherAccount.id);
                    accountManager.deleteAccount(currentAccountId);
                    this.updateAccountView();
                    this.updateMainMenu();
                    this.showMessage('账号删除成功', 'success');
                }
            } catch (error) {
                this.showError('删除失败: ' + error.message);
            }
        }
    }

    /**
     * 删除指定账号
     */
    deleteAccount(accountId) {
        const account = accountManager.getAllAccounts().find(acc => acc.id === accountId);
        if (!account) return;

        if (confirm(`确定要删除账号"${account.name}"吗？此操作不可撤销。`)) {
            try {
                accountManager.deleteAccount(accountId);
                this.updateAccountView();
                this.showMessage('账号删除成功', 'success');
            } catch (error) {
                this.showError('删除失败: ' + error.message);
            }
        }
    }

    /**
     * 导出账号数据
     */
    exportAccountData() {
        try {
            const data = accountManager.exportAccountData();
            const currentAccount = accountManager.getCurrentAccount();
            const filename = `数学训练_${currentAccount.name}_${new Date().toISOString().split('T')[0]}.json`;
            
            this.downloadFile(data, filename, 'application/json');
            this.showMessage('数据导出成功', 'success');
        } catch (error) {
            this.showError('导出失败: ' + error.message);
        }
    }

    /**
     * 导入账号数据
     */
    importAccountData() {
        const fileInput = document.getElementById('import-file-input');
        fileInput.click();
    }

    /**
     * 处理文件导入
     */
    handleFileImport(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const accountId = accountManager.importAccountData(e.target.result);
                this.updateAccountView();
                this.showMessage('数据导入成功', 'success');
            } catch (error) {
                this.showError('导入失败: ' + error.message);
            }
        };
        reader.readAsText(file);
        
        // 清空文件输入
        event.target.value = '';
    }

    /**
     * 清空账号数据
     */
    clearAccountData() {
        if (confirm('确定要清空当前账号的所有数据吗？此操作不可撤销。')) {
            try {
                accountManager.clearAccountData();
                this.updateAccountView();
                this.updateMainMenu();
                this.showMessage('数据清空成功', 'success');
            } catch (error) {
                this.showError('清空失败: ' + error.message);
            }
        }
    }

    /**
     * 显示模态框
     */
    showModal(title, message, defaultValue = '', callback = null) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <h3>${title}</h3>
                <p>${message}</p>
                <input type="text" id="modal-input" value="${defaultValue}" placeholder="请输入...">
                <div class="modal-actions">
                    <button class="primary" id="modal-confirm">确定</button>
                    <button class="secondary" id="modal-cancel">取消</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        const input = modal.querySelector('#modal-input');
        const confirmBtn = modal.querySelector('#modal-confirm');
        const cancelBtn = modal.querySelector('#modal-cancel');

        input.focus();
        input.select();

        const handleConfirm = () => {
            const value = input.value.trim();
            modal.remove();
            if (callback) callback(value);
        };

        const handleCancel = () => {
            modal.remove();
        };

        confirmBtn.addEventListener('click', handleConfirm);
        cancelBtn.addEventListener('click', handleCancel);
        
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                handleConfirm();
            } else if (e.key === 'Escape') {
                handleCancel();
            }
        });
    }

    /**
     * 下载文件
     */
    downloadFile(content, filename, contentType) {
        const blob = new Blob([content], { type: contentType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }
}