<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数学训练</title>
    <link rel="stylesheet" href="styles/main.css">
</head>

<body>
    <div id="app">
        <!-- 主菜单界面 -->
        <div id="main-menu" class="view active">
            <div class="container">
                <h1 class="app-title">数学训练</h1>
                <div class="user-info">
                    <span class="current-user">当前用户: <span id="current-user-name">默认用户</span></span>
                </div>
                <div class="level-selection"></div>
                <h2>选择练习类型</h2>
                <div class="level-buttons">
                    <button class="level-btn" data-level="within-10">10以内加减法</button>
                    <button class="level-btn" data-level="within-20">20以内加减法</button>
                    <button class="level-btn" data-level="within-100">100以内加减法</button>
                    <button class="level-btn" data-level="within-1000">1000以内加减法</button>
                    <button class="level-btn" data-level="multiplication">乘法表</button>
                    <button class="level-btn" data-level="division">除法练习</button>
                </div>

                <div class="exercise-settings">
                    <h3>练习设置</h3>
                    <div class="setting-group">
                        <label for="question-count">题目数量:</label>
                        <select id="question-count" class="setting-select">
                            <option value="10">10题</option>
                            <option value="20">20题</option>
                            <option value="30">30题</option>
                            <option value="50" selected>50题</option>
                            <option value="100">100题</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="menu-options">
                <button id="wrong-questions-btn" class="menu-btn">错题复训</button>
                <button id="progress-btn" class="menu-btn">学习进度</button>
                <button id="account-btn" class="menu-btn">账号设置</button>
            </div>
        </div>
    </div>

    <!-- 练习界面 -->
    <div id="exercise-view" class="view">
        <div class="container">
            <div class="exercise-header">
                <div class="progress-section">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <span class="progress-text">1/50</span>
                </div>
                <div class="streak-display" style="display: none;">连击 0</div>
                <div class="timer">00:00</div>
            </div>
            <div class="question-area">
                <div class="question-text">5 + 3 = ?</div>
                <input type="number" id="answer-input" class="answer-input" placeholder="请输入答案" inputmode="numeric">
                <div class="action-buttons">
                    <button id="submit-answer" class="submit-btn">提交</button>
                    <button id="hint-btn" class="hint-btn">提示</button>
                    <button id="skip-btn" class="skip-btn">跳过</button>
                </div>
            </div>
            <div class="feedback-area">
                <div id="feedback-message" class="feedback-message"></div>
            </div>
        </div>
    </div>

    <!-- 结果界面 -->
    <div id="result-view" class="view">
        <div class="container">
            <h2>练习完成！</h2>
            <div class="result-stats">
                <div class="stat-item">
                    <span class="stat-label">正确率</span>
                    <span class="stat-value" id="accuracy-rate">0%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">用时</span>
                    <span class="stat-value" id="time-spent">00:00</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">得分</span>
                    <span class="stat-value" id="points-earned">0</span>
                </div>
            </div>
            <!-- 详细结果和奖励展示将在这里动态插入 -->
            <div class="result-actions">
                <button id="review-wrong-btn" class="action-btn">查看错题</button>
                <button id="continue-practice-btn" class="action-btn primary">继续练习</button>
                <button id="back-to-menu-btn" class="action-btn">返回主菜单</button>
            </div>
        </div>
    </div>

    <!-- 错题本界面 -->
    <div id="wrong-questions-view" class="view">
        <div class="container">
            <h2>错题本</h2>
            <div class="wrong-questions-list" id="wrong-questions-list">
                <!-- 错题列表将在这里动态生成 -->
            </div>
            <div class="wrong-questions-actions">
                <button id="start-review-btn" class="action-btn primary">开始复训</button>
                <button id="back-from-wrong-btn" class="action-btn">返回</button>
            </div>
        </div>
    </div>

    <!-- 进度界面 -->
    <div id="progress-view" class="view">
        <div class="container">
            <h2>学习进度</h2>
            <div class="progress-stats">
                <div class="stat-card">
                    <h3>总积分</h3>
                    <div class="stat-number" id="total-points">0</div>
                </div>
                <div class="stat-card">
                    <h3>连续练习</h3>
                    <div class="stat-number" id="streak-days">0天</div>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="progress-chart"></canvas>
            </div>
            <div class="achievements">
                <h3>成就</h3>
                <div id="achievements-list" class="achievements-list">
                    <!-- 成就列表将在这里动态生成 -->
                </div>
            </div>
            <button id="back-from-progress-btn" class="action-btn">返回</button>
        </div>
    </div>

    <!-- 账号设置界面 -->
    <div id="account-view" class="view">
        <div class="container">
            <h2>账号设置</h2>
            <div class="account-section">
                <h3>当前账号</h3>
                <div class="current-account">
                    <div class="account-info">
                        <span class="account-name" id="current-account-name">默认用户</span>
                        <span class="account-stats" id="current-account-stats">积分: 0 | 练习: 0次</span>
                    </div>
                    <button id="edit-account-btn" class="edit-btn">编辑</button>
                </div>
            </div>

            <div class="account-section">
                <h3>账号管理</h3>
                <div class="account-actions">
                    <button id="create-account-btn" class="action-btn primary">创建新账号</button>
                    <button id="switch-account-btn" class="action-btn">切换账号</button>
                    <button id="delete-account-btn" class="action-btn danger">删除账号</button>
                </div>
            </div>

            <div class="account-section">
                <h3>数据管理</h3>
                <div class="data-actions">
                    <button id="export-data-btn" class="action-btn">导出数据</button>
                    <button id="import-data-btn" class="action-btn">导入数据</button>
                    <button id="clear-data-btn" class="action-btn danger">清空数据</button>
                </div>
                <input type="file" id="import-file-input" accept=".json" style="display: none;">
            </div>

            <div class="account-section">
                <h3>所有账号</h3>
                <div id="accounts-list" class="accounts-list">
                    <!-- 账号列表将在这里动态生成 -->
                </div>
            </div>

            <button id="back-from-account-btn" class="action-btn">返回</button>
        </div>
    </div>
    </div>

    <!-- 加载外部库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- 应用脚本 -->
    <script src="js/models/DataModels.js"></script>
    <script src="js/storage/StorageManager.js"></script>
    <script src="js/managers/QuestionBankManager.js"></script>
    <script src="js/managers/ExerciseManager.js"></script>
    <script src="js/managers/WrongQuestionManager.js"></script>
    <script src="js/managers/ProgressTracker.js"></script>
    <script src="js/managers/RewardSystem.js"></script>
    <script src="js/managers/AccountManager.js"></script>
    <script src="js/controllers/AppController.js"></script>
    <script src="js/app.js"></script>
</body>

</html>