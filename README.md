# 数学训练网页应用

一个面向学生的数学训练网页应用，提供分级别的数学练习，包含错题管理、进度跟踪和奖励机制。

## 功能特点

### 🎯 分级别练习系统
- **10以内加减法** - 适合初学者的基础练习
- **20以内加减法** - 进阶练习，需要80%正确率解锁
- **100以内加减法** - 高级练习，需要80%正确率解锁
- **乘法表** - 九九乘法表练习，需要80%正确率解锁

### 📚 题库管理系统
- 每次练习50道题，按难度循序渐进排列
- 智能题目生成，确保题目多样性
- 支持不同题库选择和自定义练习

### ❌ 错题管理系统
- 自动记录错题到错题本
- 错题复训功能，针对性改进薄弱环节
- 连续答对3次自动移除错题
- 错题筛选、排序和统计功能

### 📊 进度跟踪系统
- 详细的成绩统计和历史记录
- 可视化图表展示学习进度
- 连续练习天数统计
- 各难度级别掌握情况分析

### 🏆 奖励机制系统
- 积分奖励系统（基础分+速度奖励+连击奖励）
- 成就系统，解锁特殊称号和头像
- 庆祝动画和即时反馈
- 个人最佳记录追踪

### 📱 用户体验优化
- 响应式设计，支持移动端和桌面端
- 虚拟数字键盘（移动端）
- 键盘快捷键支持
- 流畅的动画效果和视觉反馈

## 技术架构

### 前端技术栈
- **HTML5** - 语义化标记
- **CSS3** - 响应式设计和动画效果
- **JavaScript (ES6+)** - 现代JavaScript特性
- **Chart.js** - 数据可视化图表

### 架构设计
```
┌─────────────────────────────────────┐
│           用户界面层 (UI Layer)        │
├─────────────────────────────────────┤
│         业务逻辑层 (Logic Layer)       │
├─────────────────────────────────────┤
│         数据管理层 (Data Layer)        │
├─────────────────────────────────────┤
│        本地存储层 (Storage Layer)      │
└─────────────────────────────────────┘
```

### 核心模块
- **AppController** - 应用主控制器，负责视图切换和事件处理
- **ExerciseManager** - 练习管理器，处理练习会话和答案检查
- **QuestionBankManager** - 题库管理器，生成不同难度的题目
- **WrongQuestionManager** - 错题管理器，处理错题记录和复训
- **ProgressTracker** - 进度跟踪器，统计和分析学习数据
- **RewardSystem** - 奖励系统，计算积分和成就
- **StorageManager** - 存储管理器，处理本地数据持久化

## 快速开始

### 1. 直接使用
打开 `index.html` 文件即可开始使用，无需安装任何依赖。

### 2. 功能测试
打开 `test.html` 文件可以运行系统测试，验证各个模块功能。

### 3. 本地服务器（推荐）
```bash
# 使用Python启动本地服务器
python -m http.server 8000

# 或使用Node.js
npx http-server

# 然后访问 http://localhost:8000
```

## 文件结构

```
math-training-webapp/
├── index.html              # 主应用页面
├── test.html              # 测试页面
├── README.md              # 项目说明
├── styles/
│   └── main.css           # 主样式文件
├── js/
│   ├── app.js             # 应用入口
│   ├── models/
│   │   └── DataModels.js  # 数据模型定义
│   ├── storage/
│   │   └── StorageManager.js # 存储管理
│   ├── managers/
│   │   ├── QuestionBankManager.js # 题库管理
│   │   ├── ExerciseManager.js     # 练习管理
│   │   ├── WrongQuestionManager.js # 错题管理
│   │   ├── ProgressTracker.js     # 进度跟踪
│   │   └── RewardSystem.js        # 奖励系统
│   └── controllers/
│       └── AppController.js # 应用控制器
├── tests/
│   ├── models.test.js     # 数据模型测试
│   ├── exercise.test.js   # 练习系统测试
│   └── wrong-questions.test.js # 错题管理测试
└── .kiro/specs/math-training-webapp/
    ├── requirements.md    # 需求文档
    ├── design.md         # 设计文档
    └── tasks.md          # 任务列表
```

## 使用说明

### 开始练习
1. 选择难度级别（初始只有"10以内加减法"可用）
2. 系统生成50道题目，按难度递进排列
3. 输入答案并提交，获得即时反馈
4. 完成所有题目后查看详细结果

### 错题复训
1. 点击"错题复训"查看错题本
2. 可以筛选、排序和管理错题
3. 点击"开始复训"进行专项练习
4. 连续答对3次的题目会自动移除

### 查看进度
1. 点击"学习进度"查看统计信息
2. 查看历史成绩图表和成就列表
3. 了解各难度级别的掌握情况

### 级别解锁
- 完成练习并达到80%以上正确率可解锁下一级别
- 解锁顺序：10以内 → 20以内 → 100以内 → 乘法表

## 数据存储

应用使用浏览器的 `localStorage` 进行数据持久化：
- `mathTraining_user` - 用户数据
- `mathTraining_sessions` - 练习记录
- `mathTraining_wrongQuestions` - 错题数据
- `mathTraining_settings` - 设置数据

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- 移动端浏览器支持

## 开发和测试

### 运行测试
```javascript
// 在浏览器控制台中运行
runModelTests();        // 数据模型测试
runExerciseTests();     // 练习系统测试
runWrongQuestionsTests(); // 错题管理测试
```

### 调试模式
在浏览器控制台中可以访问全局对象：
- `appController` - 应用控制器
- `exerciseManager` - 练习管理器
- `questionBankManager` - 题库管理器
- `wrongQuestionManager` - 错题管理器
- `storageManager` - 存储管理器

## 特性说明

### 题目生成算法
- **难度递进**：前10题简单，11-30题中等，31-50题困难
- **类型多样**：加法、减法、乘法题目智能生成
- **范围控制**：确保题目结果在合理范围内
- **重复避免**：尽量避免相同题目重复出现

### 奖励机制
- **基础积分**：每答对1题 = 10分
- **速度奖励**：快速答题额外奖励（≤5秒+30分，≤10秒+20分）
- **连击奖励**：连续答对奖励递增
- **完成奖励**：完成练习额外奖励（80%+30分，90%+50分）
- **成就系统**：特定条件解锁称号（完美主义者、速度之王等）

### 错题管理
- **智能记录**：自动识别和记录错题
- **优先级排序**：按错误次数和时间排序
- **掌握判定**：连续3次正确自动标记为掌握
- **复训生成**：基于错题生成相似题目进行强化练习

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 实现基础的分级练习功能
- 添加错题管理和进度跟踪
- 完成奖励机制和成就系统
- 支持响应式设计和移动端优化