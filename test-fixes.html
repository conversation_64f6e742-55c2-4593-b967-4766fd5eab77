<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数学训练 - 功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        h1 { color: #333; text-align: center; }
        h2 { color: #666; border-bottom: 2px solid #eee; padding-bottom: 10px; }
    </style>
</head>
<body>
    <h1>数学训练系统 - 修复功能测试</h1>
    
    <div class="test-section">
        <h2>1. 时间显示修复测试</h2>
        <p>测试计时器显示是否正常，无重叠问题</p>
        <div id="timer-test" style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: white;
            padding: 1rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        ">
            <div style="
                flex: 1;
                background: #e0e0e0;
                height: 10px;
                border-radius: 5px;
                position: relative;
                margin-right: 1rem;
            ">
                <div style="
                    background: linear-gradient(90deg, #4CAF50, #45a049);
                    height: 100%;
                    border-radius: 5px;
                    width: 30%;
                "></div>
                <span style="
                    position: absolute;
                    right: -80px;
                    top: -5px;
                    font-weight: bold;
                    color: #333;
                    white-space: nowrap;
                    min-width: 60px;
                ">15/50</span>
            </div>
            <div style="
                font-size: 1.2rem;
                font-weight: bold;
                color: #333;
                min-width: 80px;
                text-align: center;
                background: #f8f9fa;
                padding: 5px 10px;
                border-radius: 5px;
                border: 1px solid #dee2e6;
            " id="test-timer">02:35</div>
        </div>
        <button class="test-button" onclick="startTimerTest()">开始计时器测试</button>
        <div id="timer-test-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>2. 自动提交功能测试</h2>
        <p>输入正确答案后应该自动提交（500ms延迟）</p>
        <div style="text-align: center; padding: 2rem; background: white; border-radius: 10px; margin: 1rem 0;">
            <div style="font-size: 2rem; font-weight: bold; margin-bottom: 1rem;" id="test-question">5 + 3 = ?</div>
            <input type="number" id="test-answer-input" style="
                font-size: 1.5rem;
                padding: 1rem;
                border: 2px solid #ddd;
                border-radius: 10px;
                width: 200px;
                text-align: center;
                margin-bottom: 1rem;
            " placeholder="请输入答案">
            <div id="test-feedback" style="font-size: 1.2rem; font-weight: bold; padding: 1rem;"></div>
        </div>
        <button class="test-button" onclick="startAutoSubmitTest()">开始自动提交测试</button>
        <div id="auto-submit-test-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>3. 账号管理功能测试</h2>
        <p>测试多账号创建、切换和数据隔离</p>
        <div id="account-test-area">
            <div>当前账号: <span id="current-account-display">加载中...</span></div>
            <div>账号数量: <span id="account-count-display">0</span></div>
            <div style="margin: 1rem 0;">
                <input type="text" id="new-account-name" placeholder="输入新账号名称" style="padding: 0.5rem; margin-right: 0.5rem;">
                <button class="test-button" onclick="createTestAccount()">创建账号</button>
            </div>
            <div id="accounts-list-display" style="margin: 1rem 0;"></div>
        </div>
        <button class="test-button" onclick="startAccountTest()">开始账号管理测试</button>
        <div id="account-test-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>4. 完整应用测试</h2>
        <button class="test-button" onclick="window.open('index.html', '_blank')">打开主应用</button>
        <p>点击上方按钮打开主应用进行完整功能测试</p>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/models/DataModels.js"></script>
    <script src="js/storage/StorageManager.js"></script>
    <script src="js/managers/AccountManager.js"></script>

    <script>
        let timerInterval;
        let autoSubmitTimer;
        let testQuestion = { operand1: 5, operand2: 3, correctAnswer: 8 };

        // 1. 计时器测试
        function startTimerTest() {
            const resultDiv = document.getElementById('timer-test-result');
            const timerElement = document.getElementById('test-timer');
            
            resultDiv.innerHTML = '测试中...';
            
            let seconds = 0;
            timerInterval = setInterval(() => {
                seconds++;
                const minutes = Math.floor(seconds / 60);
                const remainingSeconds = seconds % 60;
                const timeString = `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
                timerElement.textContent = timeString;
                
                if (seconds >= 10) {
                    clearInterval(timerInterval);
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = '✅ 计时器显示正常，无重叠问题';
                }
            }, 1000);
        }

        // 2. 自动提交测试
        function startAutoSubmitTest() {
            const resultDiv = document.getElementById('auto-submit-test-result');
            const answerInput = document.getElementById('test-answer-input');
            const feedbackDiv = document.getElementById('test-feedback');
            
            resultDiv.innerHTML = '请在输入框中输入正确答案 8，观察是否自动提交...';
            
            answerInput.value = '';
            feedbackDiv.textContent = '';
            answerInput.focus();
            
            // 模拟自动提交逻辑
            answerInput.oninput = function() {
                const userAnswer = this.value.trim();
                
                if (userAnswer === '') return;
                
                const isCorrect = parseFloat(userAnswer) === testQuestion.correctAnswer;
                
                if (isCorrect) {
                    if (autoSubmitTimer) {
                        clearTimeout(autoSubmitTimer);
                    }
                    
                    autoSubmitTimer = setTimeout(() => {
                        feedbackDiv.textContent = '正确！自动提交成功';
                        feedbackDiv.style.color = '#155724';
                        feedbackDiv.style.background = '#d4edda';
                        feedbackDiv.style.borderRadius = '5px';
                        
                        resultDiv.className = 'test-result success';
                        resultDiv.innerHTML = '✅ 自动提交功能正常工作';
                    }, 500);
                } else {
                    if (autoSubmitTimer) {
                        clearTimeout(autoSubmitTimer);
                        autoSubmitTimer = null;
                    }
                    feedbackDiv.textContent = '';
                    feedbackDiv.style.background = 'transparent';
                }
            };
        }

        // 3. 账号管理测试
        function startAccountTest() {
            const resultDiv = document.getElementById('account-test-result');
            
            try {
                updateAccountDisplay();
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = '✅ 账号管理系统加载成功';
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ 账号管理系统测试失败: ' + error.message;
            }
        }

        function createTestAccount() {
            const nameInput = document.getElementById('new-account-name');
            const name = nameInput.value.trim();
            
            if (!name) {
                alert('请输入账号名称');
                return;
            }
            
            try {
                accountManager.createAccount(name);
                nameInput.value = '';
                updateAccountDisplay();
                alert('账号创建成功');
            } catch (error) {
                alert('创建失败: ' + error.message);
            }
        }

        function switchToAccount(accountId) {
            try {
                accountManager.switchAccount(accountId);
                updateAccountDisplay();
                alert('账号切换成功');
            } catch (error) {
                alert('切换失败: ' + error.message);
            }
        }

        function updateAccountDisplay() {
            const currentAccount = accountManager.getCurrentAccount();
            const accounts = accountManager.getAllAccounts();
            
            document.getElementById('current-account-display').textContent = 
                currentAccount ? currentAccount.name : '无';
            document.getElementById('account-count-display').textContent = accounts.length;
            
            const listDiv = document.getElementById('accounts-list-display');
            listDiv.innerHTML = accounts.map(account => `
                <div style="
                    padding: 0.5rem;
                    margin: 0.2rem 0;
                    background: ${account.isActive ? '#e3f2fd' : '#f8f9fa'};
                    border-radius: 5px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                ">
                    <span>${account.name} ${account.isActive ? '(当前)' : ''}</span>
                    ${!account.isActive ? `<button onclick="switchToAccount('${account.id}')" style="padding: 0.2rem 0.5rem; font-size: 0.8rem;">切换</button>` : ''}
                </div>
            `).join('');
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            console.log('测试页面加载完成');
            
            // 初始化账号显示
            if (typeof accountManager !== 'undefined') {
                updateAccountDisplay();
            }
        });
    </script>
</body>
</html>