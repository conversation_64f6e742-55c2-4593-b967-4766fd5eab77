/**
 * 端到端测试
 * 模拟真实用户操作流程
 */

class E2ETestRunner {
    constructor() {
        this.tests = [];
        this.passed = 0;
        this.failed = 0;
    }

    test(name, fn) {
        this.tests.push({ name, fn });
    }

    async run() {
        console.log('开始运行端到端测试...\n');
        
        for (const { name, fn } of this.tests) {
            try {
                await fn();
                this.passed++;
                console.log(`✅ ${name}`);
            } catch (error) {
                this.failed++;
                console.error(`❌ ${name}: ${error.message}`);
            }
        }

        console.log(`\n端到端测试完成: ${this.passed} 通过, ${this.failed} 失败`);
        return this.failed === 0;
    }

    assert(condition, message) {
        if (!condition) {
            throw new Error(message || '断言失败');
        }
    }

    assertEqual(actual, expected, message) {
        if (actual !== expected) {
            throw new Error(message || `期望 ${expected}, 实际 ${actual}`);
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 模拟点击事件
    simulateClick(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        if (element) {
            element.click();
            return true;
        }
        return false;
    }

    // 模拟输入
    simulateInput(selector, value) {
        const element = document.querySelector(selector);
        if (element) {
            element.value = value;
            element.dispatchEvent(new Event('input', { bubbles: true }));
            return true;
        }
        return false;
    }

    // 模拟按键
    simulateKeyPress(selector, key) {
        const element = document.querySelector(selector);
        if (element) {
            const event = new KeyboardEvent('keypress', {
                key: key,
                code: key === 'Enter' ? 'Enter' : `Key${key.toUpperCase()}`,
                bubbles: true
            });
            element.dispatchEvent(event);
            return true;
        }
        return false;
    }

    // 等待元素出现
    async waitForElement(selector, timeout = 5000) {
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(selector);
            if (element) {
                return element;
            }
            await this.sleep(100);
        }
        throw new Error(`元素 ${selector} 在 ${timeout}ms 内未出现`);
    }

    // 等待元素消失
    async waitForElementToDisappear(selector, timeout = 5000) {
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(selector);
            if (!element) {
                return true;
            }
            await this.sleep(100);
        }
        throw new Error(`元素 ${selector} 在 ${timeout}ms 内未消失`);
    }
}

const e2eTest = new E2ETestRunner();

// 完整用户流程测试
e2eTest.test('完整用户练习流程', async () => {
    // 清理环境
    storageManager.clear();
    
    // 1. 应用初始化
    if (typeof appController !== 'undefined') {
        appController.switchView('main-menu');
    }
    
    // 2. 检查主菜单显示
    await e2eTest.waitForElement('#main-menu.active');
    const levelButtons = document.querySelectorAll('.level-btn:not(:disabled)');
    e2eTest.assert(levelButtons.length >= 1, '应该至少有一个可用级别');
    
    // 3. 开始练习
    const firstLevelBtn = levelButtons[0];
    e2eTest.simulateClick(firstLevelBtn);
    
    // 4. 等待练习界面加载
    await e2eTest.waitForElement('#exercise-view.active');
    await e2eTest.waitForElement('.question-text');
    
    // 5. 模拟答题过程
    for (let i = 0; i < 5; i++) {
        // 获取当前题目
        const questionElement = document.querySelector('.question-text');
        e2eTest.assert(questionElement, `第${i+1}题应该显示`);
        
        // 获取题目信息
        const questionText = questionElement.textContent;
        const match = questionText.match(/(\d+)\s*([+\-×])\s*(\d+)\s*=\s*\?/);
        e2eTest.assert(match, `题目格式应该正确: ${questionText}`);
        
        const [, operand1, operator, operand2] = match;
        let correctAnswer;
        
        switch (operator) {
            case '+':
                correctAnswer = parseInt(operand1) + parseInt(operand2);
                break;
            case '-':
                correctAnswer = parseInt(operand1) - parseInt(operand2);
                break;
            case '×':
                correctAnswer = parseInt(operand1) * parseInt(operand2);
                break;
        }
        
        // 输入答案（前3题答对，后2题答错）
        const userAnswer = i < 3 ? correctAnswer : correctAnswer + 1;
        e2eTest.simulateInput('#answer-input', userAnswer.toString());
        
        // 提交答案
        e2eTest.simulateClick('#submit-answer');
        
        // 等待反馈
        await e2eTest.waitForElement('.feedback-message');
        const feedback = document.querySelector('.feedback-message');
        const isCorrect = i < 3;
        
        if (isCorrect) {
            e2eTest.assert(feedback.classList.contains('correct'), `第${i+1}题应该显示正确反馈`);
        } else {
            e2eTest.assert(feedback.classList.contains('incorrect'), `第${i+1}题应该显示错误反馈`);
        }
        
        // 等待下一题或结果页面
        await e2eTest.sleep(1600); // 等待反馈显示完成
    }
    
    // 6. 检查结果页面
    await e2eTest.waitForElement('#result-view.active');
    
    const accuracyElement = document.querySelector('#accuracy-rate');
    e2eTest.assert(accuracyElement, '应该显示正确率');
    e2eTest.assertEqual(accuracyElement.textContent, '60%', '正确率应该是60%');
    
    const pointsElement = document.querySelector('#points-earned');
    e2eTest.assert(pointsElement, '应该显示得分');
    e2eTest.assert(parseInt(pointsElement.textContent) > 0, '得分应该大于0');
});

// 错题管理流程测试
e2eTest.test('错题管理用户流程', async () => {
    // 确保有错题数据
    const question = new Question({
        type: 'addition',
        operand1: 7,
        operand2: 5,
        correctAnswer: 12
    });
    
    wrongQuestionManager.addWrongQuestion(question, 11);
    
    // 1. 从主菜单进入错题本
    if (typeof appController !== 'undefined') {
        appController.switchView('main-menu');
    }
    
    await e2eTest.waitForElement('#main-menu.active');
    e2eTest.simulateClick('#wrong-questions-btn');
    
    // 2. 检查错题本界面
    await e2eTest.waitForElement('#wrong-questions-view.active');
    await e2eTest.waitForElement('.wrong-question-item');
    
    const wrongQuestionItems = document.querySelectorAll('.wrong-question-item');
    e2eTest.assert(wrongQuestionItems.length > 0, '应该显示错题列表');
    
    // 3. 测试筛选功能
    const filterBtns = document.querySelectorAll('.filter-btn');
    if (filterBtns.length > 0) {
        e2eTest.simulateClick(filterBtns[1]); // 点击第二个筛选按钮
        await e2eTest.sleep(500);
    }
    
    // 4. 开始错题复训
    const startReviewBtn = document.querySelector('#start-review-btn');
    if (startReviewBtn && !startReviewBtn.disabled) {
        e2eTest.simulateClick(startReviewBtn);
        
        // 等待练习界面
        await e2eTest.waitForElement('#exercise-view.active');
        
        // 简单答一题
        await e2eTest.waitForElement('.question-text');
        e2eTest.simulateInput('#answer-input', '12');
        e2eTest.simulateClick('#submit-answer');
        
        await e2eTest.sleep(1000);
    }
});

// 进度查看流程测试
e2eTest.test('进度查看用户流程', async () => {
    // 1. 从主菜单进入进度页面
    if (typeof appController !== 'undefined') {
        appController.switchView('main-menu');
    }
    
    await e2eTest.waitForElement('#main-menu.active');
    e2eTest.simulateClick('#progress-btn');
    
    // 2. 检查进度界面
    await e2eTest.waitForElement('#progress-view.active');
    
    const totalPointsElement = document.querySelector('#total-points');
    e2eTest.assert(totalPointsElement, '应该显示总积分');
    
    const streakDaysElement = document.querySelector('#streak-days');
    e2eTest.assert(streakDaysElement, '应该显示连续天数');
    
    // 3. 检查图表容器
    const chartContainer = document.querySelector('.chart-container');
    e2eTest.assert(chartContainer, '应该有图表容器');
    
    // 4. 检查成就列表
    const achievementsList = document.querySelector('#achievements-list');
    e2eTest.assert(achievementsList, '应该有成就列表');
});

// 响应式设计测试
e2eTest.test('响应式设计测试', async () => {
    // 模拟移动端视口
    const originalWidth = window.innerWidth;
    const originalHeight = window.innerHeight;
    
    try {
        // 模拟移动端尺寸
        Object.defineProperty(window, 'innerWidth', {
            writable: true,
            configurable: true,
            value: 375
        });
        Object.defineProperty(window, 'innerHeight', {
            writable: true,
            configurable: true,
            value: 667
        });
        
        // 触发resize事件
        window.dispatchEvent(new Event('resize'));
        await e2eTest.sleep(500);
        
        // 检查移动端适配
        if (typeof appController !== 'undefined') {
            appController.switchView('exercise-view');
        }
        
        await e2eTest.waitForElement('#exercise-view.active');
        
        // 检查虚拟键盘是否显示（移动端）
        const virtualKeyboard = document.querySelector('.virtual-keyboard');
        if (virtualKeyboard) {
            const isVisible = window.getComputedStyle(virtualKeyboard).display !== 'none';
            e2eTest.assert(isVisible, '移动端应该显示虚拟键盘');
        }
        
        // 检查响应式布局
        const exerciseHeader = document.querySelector('.exercise-header');
        if (exerciseHeader) {
            const styles = window.getComputedStyle(exerciseHeader);
            // 在移动端应该是垂直布局
            e2eTest.assert(
                styles.flexDirection === 'column' || styles.flexDirection === 'column-reverse',
                '移动端练习头部应该是垂直布局'
            );
        }
        
    } finally {
        // 恢复原始视口尺寸
        Object.defineProperty(window, 'innerWidth', {
            writable: true,
            configurable: true,
            value: originalWidth
        });
        Object.defineProperty(window, 'innerHeight', {
            writable: true,
            configurable: true,
            value: originalHeight
        });
        
        window.dispatchEvent(new Event('resize'));
    }
});

// 键盘快捷键测试
e2eTest.test('键盘快捷键测试', async () => {
    if (typeof appController !== 'undefined') {
        appController.switchView('exercise-view');
    }
    
    await e2eTest.waitForElement('#exercise-view.active');
    
    // 测试回车键提交答案
    e2eTest.simulateInput('#answer-input', '8');
    e2eTest.simulateKeyPress('#answer-input', 'Enter');
    
    await e2eTest.sleep(500);
    
    // 检查是否有反馈
    const feedback = document.querySelector('.feedback-message');
    e2eTest.assert(feedback && feedback.textContent.length > 0, '回车键应该能提交答案');
    
    // 测试ESC键返回主菜单
    const escEvent = new KeyboardEvent('keydown', {
        key: 'Escape',
        code: 'Escape',
        bubbles: true
    });
    document.dispatchEvent(escEvent);
    
    await e2eTest.sleep(500);
    
    const mainMenu = document.querySelector('#main-menu.active');
    e2eTest.assert(mainMenu, 'ESC键应该能返回主菜单');
});

// 数据持久化测试
e2eTest.test('数据持久化测试', async () => {
    // 清理环境
    storageManager.clear();
    
    // 创建测试数据
    const testUser = new UserProfile({
        name: '持久化测试用户',
        totalPoints: 500,
        achievements: ['first-session']
    });
    
    storageManager.setUser(testUser.toJSON());
    
    // 模拟页面刷新（重新初始化）
    if (typeof initializeApp === 'function') {
        initializeApp();
    }
    
    await e2eTest.sleep(1000);
    
    // 检查数据是否恢复
    const restoredUser = storageManager.getUser();
    e2eTest.assert(restoredUser, '用户数据应该被恢复');
    e2eTest.assertEqual(restoredUser.name, testUser.name, '用户名应该正确恢复');
    e2eTest.assertEqual(restoredUser.totalPoints, testUser.totalPoints, '积分应该正确恢复');
    e2eTest.assertEqual(restoredUser.achievements.length, 1, '成就应该正确恢复');
});

// 错误处理测试
e2eTest.test('错误处理测试', async () => {
    // 测试无效输入处理
    if (typeof appController !== 'undefined') {
        appController.switchView('exercise-view');
    }
    
    await e2eTest.waitForElement('#exercise-view.active');
    
    // 输入无效答案
    e2eTest.simulateInput('#answer-input', 'abc');
    e2eTest.simulateClick('#submit-answer');
    
    await e2eTest.sleep(500);
    
    const feedback = document.querySelector('.feedback-message');
    e2eTest.assert(feedback, '应该显示错误反馈');
    
    // 测试空输入处理
    e2eTest.simulateInput('#answer-input', '');
    e2eTest.simulateClick('#submit-answer');
    
    await e2eTest.sleep(500);
    
    const emptyFeedback = document.querySelector('.feedback-message');
    e2eTest.assert(emptyFeedback && emptyFeedback.textContent.includes('请输入'), '应该提示输入答案');
});

// 运行端到端测试
if (typeof window !== 'undefined') {
    window.runE2ETests = () => e2eTest.run();
    console.log('端到端测试已加载，运行 runE2ETests() 开始测试');
} else {
    e2eTest.run();
}