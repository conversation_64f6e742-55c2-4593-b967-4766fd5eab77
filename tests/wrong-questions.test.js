/**
 * 错题管理器单元测试
 */

class WrongQuestionsTestRunner {
    constructor() {
        this.tests = [];
        this.passed = 0;
        this.failed = 0;
    }

    test(name, fn) {
        this.tests.push({ name, fn });
    }

    run() {
        console.log('开始运行错题管理测试...\n');
        
        this.tests.forEach(({ name, fn }) => {
            try {
                fn();
                this.passed++;
                console.log(`✅ ${name}`);
            } catch (error) {
                this.failed++;
                console.error(`❌ ${name}: ${error.message}`);
            }
        });

        console.log(`\n测试完成: ${this.passed} 通过, ${this.failed} 失败`);
        return this.failed === 0;
    }

    assert(condition, message) {
        if (!condition) {
            throw new Error(message || '断言失败');
        }
    }

    assertEqual(actual, expected, message) {
        if (actual !== expected) {
            throw new Error(message || `期望 ${expected}, 实际 ${actual}`);
        }
    }

    assertArrayLength(array, expectedLength, message) {
        if (array.length !== expectedLength) {
            throw new Error(message || `期望数组长度 ${expectedLength}, 实际 ${array.length}`);
        }
    }
}

const wrongQuestionsTest = new WrongQuestionsTestRunner();

// 模拟存储管理器
const mockStorageManager = {
    wrongQuestions: [],
    getWrongQuestions() {
        return [...this.wrongQuestions];
    },
    setWrongQuestions(questions) {
        this.wrongQuestions = [...questions];
    },
    reset() {
        this.wrongQuestions = [];
    }
};

// WrongQuestionManager 测试
wrongQuestionsTest.test('WrongQuestionManager - 初始化', () => {
    mockStorageManager.reset();
    const manager = new WrongQuestionManager();
    
    wrongQuestionsTest.assertEqual(manager.wrongQuestions.length, 0, '初始化时应该没有错题');
});

wrongQuestionsTest.test('WrongQuestionManager - 添加错题', () => {
    mockStorageManager.reset();
    const manager = new WrongQuestionManager();
    
    const question = new Question({
        type: 'addition',
        operand1: 5,
        operand2: 3,
        correctAnswer: 8
    });
    
    manager.addWrongQuestion(question, 7);
    
    wrongQuestionsTest.assertEqual(manager.wrongQuestions.length, 1, '应该添加一道错题');
    wrongQuestionsTest.assertEqual(manager.wrongQuestions[0].userAnswer, 7, '用户答案应该正确记录');
    wrongQuestionsTest.assertEqual(manager.wrongQuestions[0].wrongCount, 1, '错误次数应该是1');
});

wrongQuestionsTest.test('WrongQuestionManager - 重复错题处理', () => {
    mockStorageManager.reset();
    const manager = new WrongQuestionManager();
    
    const question = new Question({
        type: 'addition',
        operand1: 5,
        operand2: 3,
        correctAnswer: 8
    });
    
    // 添加同一道题两次
    manager.addWrongQuestion(question, 7);
    manager.addWrongQuestion(question, 6);
    
    wrongQuestionsTest.assertEqual(manager.wrongQuestions.length, 1, '相同题目应该只有一条记录');
    wrongQuestionsTest.assertEqual(manager.wrongQuestions[0].wrongCount, 2, '错误次数应该累加');
});

wrongQuestionsTest.test('WrongQuestionManager - 标记为正确', () => {
    mockStorageManager.reset();
    const manager = new WrongQuestionManager();
    
    const question = new Question({
        type: 'addition',
        operand1: 5,
        operand2: 3,
        correctAnswer: 8
    });
    
    manager.addWrongQuestion(question, 7);
    const wrongQuestion = manager.wrongQuestions[0];
    
    // 连续标记3次正确
    manager.markAsCorrect(wrongQuestion.id);
    manager.markAsCorrect(wrongQuestion.id);
    manager.markAsCorrect(wrongQuestion.id);
    
    wrongQuestionsTest.assert(wrongQuestion.isMastered(), '连续3次正确应该被标记为掌握');
    wrongQuestionsTest.assert(wrongQuestion.masteredAt, '应该有掌握时间');
});

wrongQuestionsTest.test('WrongQuestionManager - 获取活跃错题', () => {
    mockStorageManager.reset();
    const manager = new WrongQuestionManager();
    
    const question1 = new Question({ type: 'addition', operand1: 5, operand2: 3 });
    const question2 = new Question({ type: 'subtraction', operand1: 8, operand2: 2 });
    
    manager.addWrongQuestion(question1, 7);
    manager.addWrongQuestion(question2, 5);
    
    // 标记第一题为掌握
    const wrongQuestion1 = manager.wrongQuestions[0];
    wrongQuestion1.masteredAt = new Date();
    
    const activeQuestions = manager.getActiveWrongQuestions();
    wrongQuestionsTest.assertEqual(activeQuestions.length, 1, '应该只有1道活跃错题');
    wrongQuestionsTest.assertEqual(activeQuestions[0].question.operand1, 8, '应该是第二道题');
});

wrongQuestionsTest.test('WrongQuestionManager - 获取已掌握错题', () => {
    mockStorageManager.reset();
    const manager = new WrongQuestionManager();
    
    const question1 = new Question({ type: 'addition', operand1: 5, operand2: 3 });
    const question2 = new Question({ type: 'subtraction', operand1: 8, operand2: 2 });
    
    manager.addWrongQuestion(question1, 7);
    manager.addWrongQuestion(question2, 5);
    
    // 标记第一题为掌握
    const wrongQuestion1 = manager.wrongQuestions[0];
    wrongQuestion1.masteredAt = new Date();
    
    const masteredQuestions = manager.getMasteredQuestions();
    wrongQuestionsTest.assertEqual(masteredQuestions.length, 1, '应该有1道已掌握错题');
    wrongQuestionsTest.assertEqual(masteredQuestions[0].question.operand1, 5, '应该是第一道题');
});

wrongQuestionsTest.test('WrongQuestionManager - 生成复训会话', () => {
    mockStorageManager.reset();
    const manager = new WrongQuestionManager();
    
    // 添加多道错题
    for (let i = 1; i <= 5; i++) {
        const question = new Question({
            type: 'addition',
            operand1: i,
            operand2: i + 1,
            correctAnswer: i + i + 1
        });
        manager.addWrongQuestion(question, 0);
    }
    
    const reviewQuestions = manager.generateReviewSession(10);
    
    wrongQuestionsTest.assert(reviewQuestions.length <= 10, '复训题目数量不应超过请求数量');
    wrongQuestionsTest.assert(reviewQuestions.every(q => q instanceof Question), '所有项目都应该是Question实例');
});

wrongQuestionsTest.test('WrongQuestionManager - 生成相似题目', () => {
    mockStorageManager.reset();
    const manager = new WrongQuestionManager();
    
    const originalQuestion = new Question({
        type: 'addition',
        operand1: 5,
        operand2: 3,
        correctAnswer: 8,
        category: 'within-10'
    });
    
    const similarQuestion = manager.generateSimilarQuestion(originalQuestion);
    
    wrongQuestionsTest.assertEqual(similarQuestion.type, 'addition', '题目类型应该相同');
    wrongQuestionsTest.assertEqual(similarQuestion.category, 'within-10', '题目分类应该相同');
    wrongQuestionsTest.assert(similarQuestion.operand1 >= 0, '操作数应该非负');
    wrongQuestionsTest.assert(similarQuestion.operand2 >= 0, '操作数应该非负');
});

wrongQuestionsTest.test('WrongQuestionManager - 删除错题', () => {
    mockStorageManager.reset();
    const manager = new WrongQuestionManager();
    
    const question = new Question({
        type: 'addition',
        operand1: 5,
        operand2: 3,
        correctAnswer: 8
    });
    
    manager.addWrongQuestion(question, 7);
    const wrongQuestionId = manager.wrongQuestions[0].id;
    
    const removed = manager.removeWrongQuestion(wrongQuestionId);
    
    wrongQuestionsTest.assert(removed, '删除操作应该成功');
    wrongQuestionsTest.assertEqual(manager.wrongQuestions.length, 0, '错题应该被删除');
});

wrongQuestionsTest.test('WrongQuestionManager - 获取统计信息', () => {
    mockStorageManager.reset();
    const manager = new WrongQuestionManager();
    
    // 添加不同类型的错题
    const additionQ = new Question({ type: 'addition', operand1: 5, operand2: 3 });
    const subtractionQ = new Question({ type: 'subtraction', operand1: 8, operand2: 2 });
    
    manager.addWrongQuestion(additionQ, 7);
    manager.addWrongQuestion(subtractionQ, 5);
    
    // 标记一题为掌握
    manager.wrongQuestions[0].masteredAt = new Date();
    
    const stats = manager.getStatistics();
    
    wrongQuestionsTest.assertEqual(stats.totalWrongQuestions, 2, '总错题数应该是2');
    wrongQuestionsTest.assertEqual(stats.activeWrongQuestions, 1, '活跃错题数应该是1');
    wrongQuestionsTest.assertEqual(stats.masteredQuestions, 1, '已掌握错题数应该是1');
    wrongQuestionsTest.assertEqual(stats.masteryRate, 50, '掌握率应该是50%');
    wrongQuestionsTest.assert(stats.byType, '应该有按类型统计');
});

wrongQuestionsTest.test('WrongQuestionManager - 清理已掌握错题', () => {
    mockStorageManager.reset();
    const manager = new WrongQuestionManager();
    
    const question = new Question({
        type: 'addition',
        operand1: 5,
        operand2: 3,
        correctAnswer: 8
    });
    
    manager.addWrongQuestion(question, 7);
    
    // 设置为很久以前掌握的
    const oldDate = new Date();
    oldDate.setDate(oldDate.getDate() - 40);
    manager.wrongQuestions[0].masteredAt = oldDate;
    
    const removedCount = manager.cleanupMasteredQuestions(30);
    
    wrongQuestionsTest.assertEqual(removedCount, 1, '应该清理1道旧错题');
    wrongQuestionsTest.assertEqual(manager.wrongQuestions.length, 0, '错题应该被清理');
});

wrongQuestionsTest.test('WrongQuestionManager - 获取优先复习错题', () => {
    mockStorageManager.reset();
    const manager = new WrongQuestionManager();
    
    // 添加不同错误次数的题目
    for (let i = 1; i <= 5; i++) {
        const question = new Question({
            type: 'addition',
            operand1: i,
            operand2: i + 1,
            correctAnswer: i + i + 1
        });
        manager.addWrongQuestion(question, 0);
        manager.wrongQuestions[i - 1].wrongCount = i; // 设置不同的错误次数
    }
    
    const priorityQuestions = manager.getPriorityReviewQuestions(3);
    
    wrongQuestionsTest.assertEqual(priorityQuestions.length, 3, '应该返回3道优先题目');
    wrongQuestionsTest.assert(
        priorityQuestions[0].wrongCount >= priorityQuestions[1].wrongCount,
        '应该按优先级排序'
    );
});

wrongQuestionsTest.test('WrongQuestionManager - 导出导入数据', () => {
    mockStorageManager.reset();
    const manager = new WrongQuestionManager();
    
    const question = new Question({
        type: 'addition',
        operand1: 5,
        operand2: 3,
        correctAnswer: 8
    });
    
    manager.addWrongQuestion(question, 7);
    
    // 导出数据
    const exportData = manager.exportWrongQuestions();
    wrongQuestionsTest.assert(exportData.wrongQuestions, '导出数据应该包含错题');
    wrongQuestionsTest.assert(exportData.statistics, '导出数据应该包含统计');
    wrongQuestionsTest.assert(exportData.exportTime, '导出数据应该包含时间');
    
    // 清空并导入
    manager.resetAllWrongQuestions();
    wrongQuestionsTest.assertEqual(manager.wrongQuestions.length, 0, '重置后应该没有错题');
    
    const imported = manager.importWrongQuestions(exportData);
    wrongQuestionsTest.assert(imported, '导入应该成功');
    wrongQuestionsTest.assertEqual(manager.wrongQuestions.length, 1, '导入后应该有1道错题');
});

// 运行测试
if (typeof window !== 'undefined') {
    window.runWrongQuestionsTests = () => wrongQuestionsTest.run();
    console.log('错题管理测试已加载，运行 runWrongQuestionsTests() 开始测试');
} else {
    wrongQuestionsTest.run();
}