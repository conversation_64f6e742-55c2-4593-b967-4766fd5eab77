/**
 * 数据模型单元测试
 * 测试所有数据模型的创建、验证和方法
 */

// 简单的测试框架
class TestRunner {
    constructor() {
        this.tests = [];
        this.passed = 0;
        this.failed = 0;
    }

    test(name, fn) {
        this.tests.push({ name, fn });
    }

    run() {
        console.log('开始运行数据模型测试...\n');
        
        this.tests.forEach(({ name, fn }) => {
            try {
                fn();
                this.passed++;
                console.log(`✅ ${name}`);
            } catch (error) {
                this.failed++;
                console.error(`❌ ${name}: ${error.message}`);
            }
        });

        console.log(`\n测试完成: ${this.passed} 通过, ${this.failed} 失败`);
        return this.failed === 0;
    }

    assert(condition, message) {
        if (!condition) {
            throw new Error(message || '断言失败');
        }
    }

    assertEqual(actual, expected, message) {
        if (actual !== expected) {
            throw new Error(message || `期望 ${expected}, 实际 ${actual}`);
        }
    }

    assertArrayEqual(actual, expected, message) {
        if (JSON.stringify(actual) !== JSON.stringify(expected)) {
            throw new Error(message || `数组不相等: 期望 ${JSON.stringify(expected)}, 实际 ${JSON.stringify(actual)}`);
        }
    }
}

const test = new TestRunner();

// UserProfile 测试
test.test('UserProfile - 默认构造', () => {
    const user = new UserProfile();
    test.assert(user.id, '应该有ID');
    test.assertEqual(user.name, '学生', '默认名称应该是"学生"');
    test.assertEqual(user.level, 1, '默认等级应该是1');
    test.assertEqual(user.totalPoints, 0, '默认积分应该是0');
    test.assertArrayEqual(user.achievements, [], '默认成就应该是空数组');
    test.assertArrayEqual(user.unlockedLevels, ['within-10'], '默认解锁级别应该包含within-10');
});

test.test('UserProfile - 自定义数据构造', () => {
    const userData = {
        name: '小明',
        level: 5,
        totalPoints: 1000,
        achievements: ['first-100'],
        unlockedLevels: ['within-10', 'within-20']
    };
    const user = new UserProfile(userData);
    test.assertEqual(user.name, '小明');
    test.assertEqual(user.level, 5);
    test.assertEqual(user.totalPoints, 1000);
    test.assertArrayEqual(user.achievements, ['first-100']);
    test.assertArrayEqual(user.unlockedLevels, ['within-10', 'within-20']);
});

test.test('UserProfile - 验证方法', () => {
    const validUser = new UserProfile();
    test.assertEqual(validUser.validate().length, 0, '有效用户应该通过验证');
    
    const invalidUser = new UserProfile({ level: -1, totalPoints: -100 });
    const errors = invalidUser.validate();
    test.assert(errors.length > 0, '无效用户应该有验证错误');
    test.assert(errors.some(e => e.includes('等级')), '应该有等级错误');
    test.assert(errors.some(e => e.includes('积分')), '应该有积分错误');
});

test.test('UserProfile - JSON序列化', () => {
    const user = new UserProfile({ name: '测试用户' });
    const json = user.toJSON();
    test.assertEqual(typeof json, 'object', 'toJSON应该返回对象');
    test.assertEqual(json.name, '测试用户', 'JSON应该包含正确的名称');
    test.assert(json.id, 'JSON应该包含ID');
});

// Question 测试
test.test('Question - 加法题目', () => {
    const question = new Question({
        type: 'addition',
        operand1: 5,
        operand2: 3
    });
    test.assertEqual(question.type, 'addition');
    test.assertEqual(question.operand1, 5);
    test.assertEqual(question.operand2, 3);
    test.assertEqual(question.correctAnswer, 8, '5+3应该等于8');
    test.assertEqual(question.getExpression(), '5 + 3', '表达式格式应该正确');
});

test.test('Question - 减法题目', () => {
    const question = new Question({
        type: 'subtraction',
        operand1: 10,
        operand2: 4
    });
    test.assertEqual(question.correctAnswer, 6, '10-4应该等于6');
    test.assertEqual(question.getExpression(), '10 - 4', '减法表达式应该正确');
});

test.test('Question - 乘法题目', () => {
    const question = new Question({
        type: 'multiplication',
        operand1: 7,
        operand2: 8
    });
    test.assertEqual(question.correctAnswer, 56, '7×8应该等于56');
    test.assertEqual(question.getExpression(), '7 × 8', '乘法表达式应该正确');
});

test.test('Question - 验证方法', () => {
    const validQuestion = new Question();
    test.assertEqual(validQuestion.validate().length, 0, '有效题目应该通过验证');
    
    const invalidQuestion = new Question({ type: 'invalid', operand1: 'not-number' });
    const errors = invalidQuestion.validate();
    test.assert(errors.length > 0, '无效题目应该有验证错误');
});

// ExerciseSession 测试
test.test('ExerciseSession - 默认构造', () => {
    const session = new ExerciseSession();
    test.assert(session.id, '应该有ID');
    test.assertEqual(session.level, 'within-10', '默认级别应该是within-10');
    test.assertArrayEqual(session.questions, [], '默认题目列表应该是空数组');
    test.assertArrayEqual(session.answers, [], '默认答案列表应该是空数组');
    test.assertEqual(session.correctCount, 0, '默认正确数应该是0');
    test.assertEqual(session.totalCount, 0, '默认总数应该是0');
});

test.test('ExerciseSession - 添加答案', () => {
    const session = new ExerciseSession();
    session.addAnswer('q1', 8, true, 5);
    session.addAnswer('q2', 10, false, 8);
    
    test.assertEqual(session.answers.length, 2, '应该有2个答案');
    test.assertEqual(session.correctCount, 1, '应该有1个正确答案');
    test.assertEqual(session.totalCount, 2, '总数应该是2');
});

test.test('ExerciseSession - 计算正确率', () => {
    const session = new ExerciseSession();
    test.assertEqual(session.calculateAccuracy(), 0, '空会话正确率应该是0');
    
    session.addAnswer('q1', 8, true);
    session.addAnswer('q2', 10, false);
    session.addAnswer('q3', 5, true);
    
    test.assertEqual(session.calculateAccuracy(), 67, '2/3正确率应该是67%');
});

test.test('ExerciseSession - 完成会话', () => {
    const session = new ExerciseSession();
    session.addAnswer('q1', 8, true);
    session.addAnswer('q2', 5, true);
    
    session.complete();
    
    test.assert(session.endTime, '完成后应该有结束时间');
    test.assert(session.score > 0, '完成后应该有分数');
});

// WrongQuestion 测试
test.test('WrongQuestion - 创建错题', () => {
    const question = new Question({ type: 'addition', operand1: 5, operand2: 3 });
    const wrongQ = new WrongQuestion({
        question: question,
        userAnswer: 7,
        correctAnswer: 8
    });
    
    test.assertEqual(wrongQ.userAnswer, 7);
    test.assertEqual(wrongQ.correctAnswer, 8);
    test.assertEqual(wrongQ.wrongCount, 1, '初始错误次数应该是1');
    test.assertEqual(wrongQ.correctStreak, 0, '初始连续正确次数应该是0');
    test.assert(!wrongQ.isMastered(), '初始状态不应该被掌握');
});

test.test('WrongQuestion - 标记正确', () => {
    const question = new Question();
    const wrongQ = new WrongQuestion({ question });
    
    wrongQ.markCorrect();
    test.assertEqual(wrongQ.correctStreak, 1, '连续正确次数应该增加');
    
    wrongQ.markCorrect();
    wrongQ.markCorrect();
    test.assert(wrongQ.isMastered(), '连续3次正确应该被掌握');
    test.assert(wrongQ.masteredAt, '应该有掌握时间');
});

test.test('WrongQuestion - 标记错误', () => {
    const question = new Question();
    const wrongQ = new WrongQuestion({ question });
    
    wrongQ.correctStreak = 2;
    wrongQ.markWrong();
    
    test.assertEqual(wrongQ.wrongCount, 2, '错误次数应该增加');
    test.assertEqual(wrongQ.correctStreak, 0, '连续正确次数应该重置');
});

// Achievement 测试
test.test('Achievement - 创建成就', () => {
    const achievement = new Achievement({
        id: 'first-100',
        name: '百题达人',
        description: '完成100道题目',
        points: 100
    });
    
    test.assertEqual(achievement.id, 'first-100');
    test.assertEqual(achievement.name, '百题达人');
    test.assertEqual(achievement.points, 100);
    test.assert(!achievement.isUnlocked(), '初始状态不应该解锁');
});

test.test('Achievement - 解锁成就', () => {
    const achievement = new Achievement({ id: 'test', name: '测试' });
    
    achievement.unlock();
    test.assert(achievement.isUnlocked(), '解锁后应该返回true');
    test.assert(achievement.unlockedAt, '应该有解锁时间');
    
    // 重复解锁不应该改变时间
    const firstUnlockTime = achievement.unlockedAt;
    achievement.unlock();
    test.assertEqual(achievement.unlockedAt, firstUnlockTime, '重复解锁不应该改变时间');
});

test.test('Achievement - 验证方法', () => {
    const validAchievement = new Achievement({
        id: 'test',
        name: '测试成就',
        description: '测试描述',
        condition: { type: 'score', value: 100 },
        points: 50
    });
    test.assertEqual(validAchievement.validate().length, 0, '有效成就应该通过验证');
    
    const invalidAchievement = new Achievement({ points: -10 });
    const errors = invalidAchievement.validate();
    test.assert(errors.length > 0, '无效成就应该有验证错误');
});

// 运行所有测试
if (typeof window !== 'undefined') {
    // 浏览器环境
    window.runModelTests = () => test.run();
    console.log('数据模型测试已加载，运行 runModelTests() 开始测试');
} else {
    // Node.js环境
    test.run();
}