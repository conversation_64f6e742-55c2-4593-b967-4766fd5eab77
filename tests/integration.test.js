/**
 * 集成测试
 * 测试各模块间的协作和数据流
 */

class IntegrationTestRunner {
    constructor() {
        this.tests = [];
        this.passed = 0;
        this.failed = 0;
    }

    test(name, fn) {
        this.tests.push({ name, fn });
    }

    async run() {
        console.log('开始运行集成测试...\n');
        
        for (const { name, fn } of this.tests) {
            try {
                await fn();
                this.passed++;
                console.log(`✅ ${name}`);
            } catch (error) {
                this.failed++;
                console.error(`❌ ${name}: ${error.message}`);
            }
        }

        console.log(`\n集成测试完成: ${this.passed} 通过, ${this.failed} 失败`);
        return this.failed === 0;
    }

    assert(condition, message) {
        if (!condition) {
            throw new Error(message || '断言失败');
        }
    }

    assertEqual(actual, expected, message) {
        if (actual !== expected) {
            throw new Error(message || `期望 ${expected}, 实际 ${actual}`);
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

const integrationTest = new IntegrationTestRunner();

// 清理测试环境
function cleanupTestEnvironment() {
    storageManager.clear();
    if (typeof exerciseManager !== 'undefined') {
        exerciseManager.currentSession = null;
        exerciseManager.isActive = false;
    }
}

// 完整练习流程测试
integrationTest.test('完整练习流程集成测试', async () => {
    cleanupTestEnvironment();
    
    // 1. 初始化用户
    const user = new UserProfile({
        name: '测试用户',
        level: 1,
        totalPoints: 0,
        unlockedLevels: ['within-10']
    });
    storageManager.setUser(user.toJSON());
    
    // 2. 开始练习会话
    const session = exerciseManager.startSession('within-10', 5);
    integrationTest.assert(session, '应该成功创建练习会话');
    integrationTest.assertEqual(session.level, 'within-10', '会话级别应该正确');
    
    // 3. 模拟答题过程
    for (let i = 0; i < 5; i++) {
        const question = exerciseManager.getCurrentQuestion();
        integrationTest.assert(question, `第${i+1}题应该存在`);
        
        // 模拟答对3题，答错2题
        const isCorrect = i < 3;
        const userAnswer = isCorrect ? question.correctAnswer : question.correctAnswer + 1;
        
        const result = exerciseManager.checkAnswer(userAnswer);
        integrationTest.assertEqual(result.isCorrect, isCorrect, `第${i+1}题答题结果应该正确`);
    }
    
    // 4. 完成会话
    const result = exerciseManager.completeSession();
    integrationTest.assert(result, '应该成功完成会话');
    integrationTest.assertEqual(result.correctCount, 3, '正确数应该是3');
    integrationTest.assertEqual(result.totalCount, 5, '总数应该是5');
    
    // 5. 检查数据持久化
    const sessions = storageManager.getSessions();
    integrationTest.assertEqual(sessions.length, 1, '应该保存1个会话记录');
    
    const wrongQuestions = storageManager.getWrongQuestions();
    integrationTest.assertEqual(wrongQuestions.length, 2, '应该记录2道错题');
    
    // 6. 检查用户数据更新
    const updatedUser = storageManager.getUser();
    integrationTest.assert(updatedUser.totalPoints > 0, '用户积分应该增加');
});

// 错题管理集成测试
integrationTest.test('错题管理系统集成测试', async () => {
    cleanupTestEnvironment();
    
    // 1. 创建错题
    const question1 = new Question({
        type: 'addition',
        operand1: 5,
        operand2: 3,
        correctAnswer: 8
    });
    
    const question2 = new Question({
        type: 'subtraction',
        operand1: 10,
        operand2: 4,
        correctAnswer: 6
    });
    
    wrongQuestionManager.addWrongQuestion(question1, 7);
    wrongQuestionManager.addWrongQuestion(question2, 5);
    
    // 2. 检查错题记录
    const activeWrongQuestions = wrongQuestionManager.getActiveWrongQuestions();
    integrationTest.assertEqual(activeWrongQuestions.length, 2, '应该有2道活跃错题');
    
    // 3. 生成复训会话
    const reviewQuestions = wrongQuestionManager.generateReviewSession(10);
    integrationTest.assert(reviewQuestions.length > 0, '应该生成复训题目');
    
    // 4. 模拟复训过程
    const wrongQuestion = activeWrongQuestions[0];
    wrongQuestionManager.markAsCorrect(wrongQuestion.id);
    wrongQuestionManager.markAsCorrect(wrongQuestion.id);
    wrongQuestionManager.markAsCorrect(wrongQuestion.id);
    
    // 5. 检查掌握状态
    const masteredQuestions = wrongQuestionManager.getMasteredQuestions();
    integrationTest.assertEqual(masteredQuestions.length, 1, '应该有1道已掌握错题');
});

// 进度跟踪集成测试
integrationTest.test('进度跟踪系统集成测试', async () => {
    cleanupTestEnvironment();
    
    // 1. 创建用户和会话数据
    const user = new UserProfile({
        name: '测试用户',
        totalPoints: 500
    });
    storageManager.setUser(user.toJSON());
    
    const sessionData = {
        id: 'test_session',
        userId: user.id,
        level: 'within-10',
        startTime: new Date(Date.now() - 300000), // 5分钟前
        endTime: new Date(),
        score: 80,
        correctCount: 8,
        totalCount: 10
    };
    
    // 2. 记录会话
    const recorded = progressTracker.recordSession(sessionData);
    integrationTest.assert(recorded, '应该成功记录会话');
    
    // 3. 获取进度数据
    const progressData = progressTracker.getProgressData();
    integrationTest.assertEqual(progressData.totalSessions, 1, '总会话数应该是1');
    integrationTest.assertEqual(progressData.overallAccuracy, 80, '总体正确率应该是80%');
    
    // 4. 检查统计数据
    const statistics = progressTracker.calculateStatistics();
    integrationTest.assert(statistics.daily, '应该有每日统计');
    integrationTest.assert(statistics.trends, '应该有趋势数据');
});

// 奖励系统集成测试
integrationTest.test('奖励系统集成测试', async () => {
    cleanupTestEnvironment();
    
    // 1. 创建高分会话数据
    const sessionData = {
        correctCount: 10,
        totalCount: 10,
        answers: Array(10).fill({ isCorrect: true, timeSpent: 3 }),
        startTime: new Date(Date.now() - 180000), // 3分钟前
        endTime: new Date()
    };
    
    // 2. 计算积分
    const pointsResult = rewardSystem.calculatePoints(sessionData);
    integrationTest.assert(pointsResult.totalPoints > 100, '完美表现应该获得高分');
    integrationTest.assert(pointsResult.breakdown.speedBonus > 0, '应该有速度奖励');
    integrationTest.assert(pointsResult.breakdown.accuracyBonus > 0, '应该有正确率奖励');
    
    // 3. 检查成就
    const userData = { achievements: [], totalPoints: 0 };
    const achievements = rewardSystem.checkAchievements(sessionData, userData);
    integrationTest.assert(achievements.length > 0, '应该解锁成就');
    
    // 4. 检查用户等级
    const userLevel = rewardSystem.getUserLevel(1000);
    integrationTest.assertEqual(userLevel.name, '大师', '1000分应该是大师级别');
});

// 级别解锁集成测试
integrationTest.test('级别解锁系统集成测试', async () => {
    cleanupTestEnvironment();
    
    // 1. 创建用户，只解锁第一级别
    const user = new UserProfile({
        name: '测试用户',
        unlockedLevels: ['within-10']
    });
    storageManager.setUser(user.toJSON());
    
    // 2. 模拟高正确率完成练习
    const session = exerciseManager.startSession('within-10', 10);
    
    // 模拟90%正确率
    for (let i = 0; i < 10; i++) {
        const question = exerciseManager.getCurrentQuestion();
        const isCorrect = i < 9; // 前9题答对
        const userAnswer = isCorrect ? question.correctAnswer : question.correctAnswer + 1;
        exerciseManager.checkAnswer(userAnswer);
    }
    
    // 3. 完成会话并检查解锁
    const result = exerciseManager.completeSession();
    integrationTest.assertEqual(result.accuracy, 90, '正确率应该是90%');
    
    // 4. 检查级别解锁
    const updatedUser = storageManager.getUser();
    integrationTest.assert(
        updatedUser.unlockedLevels.includes('within-20'),
        '应该解锁20以内级别'
    );
});

// 数据持久化集成测试
integrationTest.test('数据持久化集成测试', async () => {
    cleanupTestEnvironment();
    
    // 1. 创建完整的应用数据
    const user = new UserProfile({
        name: '持久化测试用户',
        totalPoints: 1000,
        achievements: ['first-session', 'accuracy-80']
    });
    
    const session = new ExerciseSession({
        userId: user.id,
        level: 'within-10',
        score: 100,
        correctCount: 10,
        totalCount: 10
    });
    
    const wrongQuestion = new WrongQuestion({
        question: new Question({
            type: 'addition',
            operand1: 7,
            operand2: 5,
            correctAnswer: 12
        }),
        userAnswer: 11
    });
    
    // 2. 保存所有数据
    storageManager.setUser(user.toJSON());
    storageManager.addSession(session.toJSON());
    storageManager.addWrongQuestion(wrongQuestion.toJSON());
    
    // 3. 验证数据完整性
    const savedUser = storageManager.getUser();
    const savedSessions = storageManager.getSessions();
    const savedWrongQuestions = storageManager.getWrongQuestions();
    
    integrationTest.assertEqual(savedUser.name, user.name, '用户数据应该正确保存');
    integrationTest.assertEqual(savedSessions.length, 1, '会话数据应该正确保存');
    integrationTest.assertEqual(savedWrongQuestions.length, 1, '错题数据应该正确保存');
    
    // 4. 测试数据导出导入
    const exportData = storageManager.exportData();
    integrationTest.assert(exportData, '应该能导出数据');
    
    storageManager.clear();
    const imported = storageManager.importData(exportData);
    integrationTest.assert(imported, '应该能导入数据');
    
    const restoredUser = storageManager.getUser();
    integrationTest.assertEqual(restoredUser.name, user.name, '导入的用户数据应该正确');
});

// 运行集成测试
if (typeof window !== 'undefined') {
    window.runIntegrationTests = () => integrationTest.run();
    console.log('集成测试已加载，运行 runIntegrationTests() 开始测试');
} else {
    integrationTest.run();
}