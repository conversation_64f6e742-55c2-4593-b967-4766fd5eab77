/**
 * 练习管理器和题库管理器单元测试
 */

class ExerciseTestRunner {
    constructor() {
        this.tests = [];
        this.passed = 0;
        this.failed = 0;
    }

    test(name, fn) {
        this.tests.push({ name, fn });
    }

    run() {
        console.log('开始运行练习系统测试...\n');
        
        this.tests.forEach(({ name, fn }) => {
            try {
                fn();
                this.passed++;
                console.log(`✅ ${name}`);
            } catch (error) {
                this.failed++;
                console.error(`❌ ${name}: ${error.message}`);
            }
        });

        console.log(`\n测试完成: ${this.passed} 通过, ${this.failed} 失败`);
        return this.failed === 0;
    }

    assert(condition, message) {
        if (!condition) {
            throw new Error(message || '断言失败');
        }
    }

    assertEqual(actual, expected, message) {
        if (actual !== expected) {
            throw new Error(message || `期望 ${expected}, 实际 ${actual}`);
        }
    }

    assertInRange(value, min, max, message) {
        if (value < min || value > max) {
            throw new Error(message || `值 ${value} 不在范围 [${min}, ${max}] 内`);
        }
    }
}

const exerciseTest = new ExerciseTestRunner();

// QuestionBankManager 测试
exerciseTest.test('QuestionBankManager - 获取可用题库', () => {
    const banks = questionBankManager.getAvailableBanks();
    exerciseTest.assert(Array.isArray(banks), '应该返回数组');
    exerciseTest.assert(banks.length > 0, '应该有可用题库');
    
    const within10Bank = banks.find(b => b.id === 'within-10');
    exerciseTest.assert(within10Bank, '应该包含10以内题库');
    exerciseTest.assertEqual(within10Bank.name, '10以内加减法');
});

exerciseTest.test('QuestionBankManager - 生成10以内加法题目', () => {
    const question = questionBankManager.generateSingleQuestion('within-10', 1);
    
    exerciseTest.assertEqual(question.category, 'within-10');
    exerciseTest.assertInRange(question.operand1, 0, 10, '操作数1应该在0-10范围内');
    exerciseTest.assertInRange(question.operand2, 0, 10, '操作数2应该在0-10范围内');
    
    if (question.type === 'addition') {
        exerciseTest.assertInRange(question.correctAnswer, 0, 20, '加法结果应该合理');
        exerciseTest.assertEqual(question.correctAnswer, question.operand1 + question.operand2);
    } else if (question.type === 'subtraction') {
        exerciseTest.assert(question.correctAnswer >= 0, '减法结果不能为负');
        exerciseTest.assertEqual(question.correctAnswer, question.operand1 - question.operand2);
    }
});

exerciseTest.test('QuestionBankManager - 生成乘法题目', () => {
    const question = questionBankManager.generateSingleQuestion('multiplication', 2);
    
    exerciseTest.assertEqual(question.type, 'multiplication');
    exerciseTest.assertEqual(question.category, 'multiplication');
    exerciseTest.assertInRange(question.operand1, 1, 9, '乘法操作数1应该在1-9范围内');
    exerciseTest.assertInRange(question.operand2, 1, 9, '乘法操作数2应该在1-9范围内');
    exerciseTest.assertEqual(question.correctAnswer, question.operand1 * question.operand2);
});

exerciseTest.test('QuestionBankManager - 生成指定数量题目', () => {
    const questions = questionBankManager.generateQuestions('within-10', 20);
    
    exerciseTest.assertEqual(questions.length, 20, '应该生成指定数量的题目');
    
    // 检查难度递进
    const firstTenQuestions = questions.slice(0, 10);
    const allEasyDifficulty = firstTenQuestions.every(q => q.difficulty === 1);
    exerciseTest.assert(allEasyDifficulty, '前10题应该都是简单难度');
});

exerciseTest.test('QuestionBankManager - 题目验证', () => {
    const validQuestion = new Question({
        type: 'addition',
        operand1: 5,
        operand2: 3,
        category: 'within-10'
    });
    
    exerciseTest.assert(
        questionBankManager.validateQuestion(validQuestion, 'within-10'),
        '有效题目应该通过验证'
    );
    
    const invalidQuestion = new Question({
        type: 'multiplication',
        operand1: 5,
        operand2: 3,
        category: 'within-10'
    });
    
    exerciseTest.assert(
        !questionBankManager.validateQuestion(invalidQuestion, 'within-10'),
        '无效题目应该验证失败'
    );
});

exerciseTest.test('QuestionBankManager - 生成复习题目', () => {
    const wrongQuestions = [
        new WrongQuestion({
            question: new Question({ type: 'addition', operand1: 5, operand2: 3 })
        }),
        new WrongQuestion({
            question: new Question({ type: 'subtraction', operand1: 8, operand2: 2 })
        })
    ];
    
    const reviewQuestions = questionBankManager.generateReviewQuestions(wrongQuestions, 10);
    
    exerciseTest.assertEqual(reviewQuestions.length, 10, '应该生成指定数量的复习题目');
    exerciseTest.assert(reviewQuestions.every(q => q instanceof Question), '所有项目都应该是Question实例');
});

// ExerciseManager 测试
exerciseTest.test('ExerciseManager - 开始练习会话', () => {
    const session = exerciseManager.startSession('within-10', 10);
    
    exerciseTest.assert(session instanceof ExerciseSession, '应该返回ExerciseSession实例');
    exerciseTest.assertEqual(session.level, 'within-10');
    exerciseTest.assertEqual(session.questions.length, 10);
    exerciseTest.assert(exerciseManager.isActive, '练习应该处于活跃状态');
});

exerciseTest.test('ExerciseManager - 获取当前题目', () => {
    exerciseManager.startSession('within-10', 5);
    const question = exerciseManager.getCurrentQuestion();
    
    exerciseTest.assert(question instanceof Question, '应该返回Question实例');
    exerciseTest.assert(question.id, '题目应该有ID');
});

exerciseTest.test('ExerciseManager - 获取练习进度', () => {
    exerciseManager.startSession('within-10', 10);
    const progress = exerciseManager.getProgress();
    
    exerciseTest.assertEqual(progress.current, 1, '当前题目应该是第1题');
    exerciseTest.assertEqual(progress.total, 10, '总题目数应该是10');
    exerciseTest.assertEqual(progress.percentage, 10, '进度百分比应该是10%');
});

exerciseTest.test('ExerciseManager - 检查正确答案', () => {
    exerciseManager.startSession('within-10', 5);
    const question = exerciseManager.getCurrentQuestion();
    const result = exerciseManager.checkAnswer(question.correctAnswer);
    
    exerciseTest.assert(result.isCorrect, '正确答案应该被标记为正确');
    exerciseTest.assertEqual(result.correctAnswer, question.correctAnswer);
    exerciseTest.assert(typeof result.timeSpent === 'number', '应该记录答题时间');
});

exerciseTest.test('ExerciseManager - 检查错误答案', () => {
    exerciseManager.startSession('within-10', 5);
    const question = exerciseManager.getCurrentQuestion();
    const wrongAnswer = question.correctAnswer + 1;
    const result = exerciseManager.checkAnswer(wrongAnswer);
    
    exerciseTest.assert(!result.isCorrect, '错误答案应该被标记为错误');
    exerciseTest.assertEqual(result.correctAnswer, question.correctAnswer);
});

exerciseTest.test('ExerciseManager - 答案验证', () => {
    exerciseTest.assert(exerciseManager.validateAnswer(5, 5), '相同数字应该验证通过');
    exerciseTest.assert(exerciseManager.validateAnswer('5', 5), '字符串数字应该验证通过');
    exerciseTest.assert(!exerciseManager.validateAnswer('abc', 5), '非数字应该验证失败');
    exerciseTest.assert(!exerciseManager.validateAnswer(5, 6), '不同数字应该验证失败');
});

exerciseTest.test('ExerciseManager - 跳过题目', () => {
    exerciseManager.startSession('within-10', 5);
    const initialIndex = exerciseManager.currentQuestionIndex;
    const result = exerciseManager.skipQuestion();
    
    exerciseTest.assert(!result.isCorrect, '跳过的题目应该标记为错误');
    exerciseTest.assertEqual(exerciseManager.currentQuestionIndex, initialIndex + 1, '应该移动到下一题');
});

exerciseTest.test('ExerciseManager - 获取会话统计', () => {
    exerciseManager.startSession('within-10', 5);
    const stats = exerciseManager.getSessionStats();
    
    exerciseTest.assertEqual(stats.level, 'within-10');
    exerciseTest.assert(typeof stats.accuracy === 'number', '正确率应该是数字');
    exerciseTest.assert(typeof stats.timeSpent === 'number', '用时应该是数字');
    exerciseTest.assert(stats.isActive, '会话应该处于活跃状态');
});

exerciseTest.test('ExerciseManager - 获取题目提示', () => {
    exerciseManager.startSession('within-10', 5);
    const hint = exerciseManager.getHint();
    
    exerciseTest.assert(typeof hint === 'string', '提示应该是字符串');
    exerciseTest.assert(hint.length > 0, '提示不应该为空');
});

exerciseTest.test('ExerciseManager - 计算连击数', () => {
    exerciseManager.startSession('within-10', 5);
    
    // 初始连击数应该是0
    exerciseTest.assertEqual(exerciseManager.getCurrentStreak(), 0);
    
    // 答对一题
    const question1 = exerciseManager.getCurrentQuestion();
    exerciseManager.checkAnswer(question1.correctAnswer);
    exerciseTest.assertEqual(exerciseManager.getCurrentStreak(), 1);
    
    // 再答对一题
    const question2 = exerciseManager.getCurrentQuestion();
    exerciseManager.checkAnswer(question2.correctAnswer);
    exerciseTest.assertEqual(exerciseManager.getCurrentStreak(), 2);
    
    // 答错一题，连击数应该重置
    const question3 = exerciseManager.getCurrentQuestion();
    exerciseManager.checkAnswer(question3.correctAnswer + 1);
    exerciseTest.assertEqual(exerciseManager.getCurrentStreak(), 0);
});

// 运行测试
if (typeof window !== 'undefined') {
    window.runExerciseTests = () => exerciseTest.run();
    console.log('练习系统测试已加载，运行 runExerciseTests() 开始测试');
} else {
    exerciseTest.run();
}