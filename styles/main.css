/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* 视图管理 */
.view {
    display: none;
    min-height: 100vh;
    padding: 20px 0;
}

.view.active {
    display: block;
}

/* 主菜单样式 */
.app-title {
    text-align: center;
    color: white;
    font-size: 2.5rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.user-info {
    text-align: center;
    margin-bottom: 2rem;
}

.current-user {
    color: white;
    font-size: 1.1rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

#current-user-name {
    font-weight: bold;
}

.level-selection {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.level-selection h2 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: #333;
}

.level-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

/* 练习设置样式 */
.exercise-settings {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 12px;
    margin-top: 2rem;
    border: 1px solid #e9ecef;
}

.exercise-settings h3 {
    margin: 0 0 1rem 0;
    color: #495057;
    font-size: 1.1rem;
}

.setting-group {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.setting-group label {
    font-weight: 600;
    color: #495057;
    min-width: 80px;
}

.setting-select {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    background: white;
    font-size: 0.9rem;
    color: #495057;
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.setting-select:hover {
    border-color: #667eea;
}

.setting-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.level-btn {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    border: none;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.level-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.level-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.menu-options {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.menu-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid white;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.menu-btn:hover {
    background: white;
    color: #667eea;
}

/* 练习界面样式 */
.exercise-header {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    position: relative;
    gap: 2rem;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.progress-section {
    flex: 2;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 200px;
    max-width: 400px;
}

.progress-bar {
    flex: 1;
    background: #f0f2f5;
    height: 12px;
    border-radius: 6px;
    position: relative;
    border: 1px solid rgba(0,0,0,0.05);
}

.progress-fill {
    background: linear-gradient(90deg, #667eea, #764ba2);
    height: 100%;
    border-radius: 6px;
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    width: 2%;
    box-shadow: 0 1px 3px rgba(102, 126, 234, 0.3);
}

.progress-text {
    font-weight: 600;
    color: #4a5568;
    white-space: nowrap;
    min-width: 50px;
    font-size: 0.9rem;
    background: white;
    padding: 4px 10px;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #e2e8f0;
    text-align: center;
    flex-shrink: 0;
}

.timer {
    font-size: 1.1rem;
    font-weight: 600;
    color: #4a5568;
    min-width: 90px;
    width: 90px;
    text-align: center;
    background: linear-gradient(135deg, #f7fafc, #edf2f7);
    padding: 8px 16px;
    border-radius: 20px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    flex-shrink: 0;
}

.question-area {
    background: white;
    border-radius: 15px;
    padding: 3rem;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.question-text {
    font-size: 2.5rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 2rem;
}

.answer-input {
    font-size: 1.5rem;
    padding: 1rem;
    border: 2px solid #ddd;
    border-radius: 10px;
    width: 200px;
    text-align: center;
    margin-bottom: 1.5rem;
    transition: border-color 0.3s ease;
}

.answer-input:focus {
    outline: none;
    border-color: #4CAF50;
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 1.5rem;
}

.submit-btn {
    background: linear-gradient(45deg, #2196F3, #1976D2);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
}

.feedback-area {
    text-align: center;
}

.feedback-message {
    font-size: 1.2rem;
    font-weight: bold;
    padding: 1rem;
    border-radius: 10px;
    margin: 1rem 0;
    transition: all 0.3s ease;
}

.feedback-message.correct {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.feedback-message.incorrect {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 结果界面样式 */
.result-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.result-details {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin: 2rem 0;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.detail-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.detail-item {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.detail-label {
    display: block;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.detail-value {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
}

.wrong-answers-summary h4 {
    color: #333;
    margin-bottom: 1rem;
    border-bottom: 2px solid #eee;
    padding-bottom: 0.5rem;
}

.wrong-answers-list {
    max-height: 200px;
    overflow-y: auto;
}

.wrong-answer-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem;
    margin-bottom: 0.5rem;
    background: #fff5f5;
    border-radius: 8px;
    border-left: 4px solid #dc3545;
}

.wrong-question {
    font-weight: bold;
    color: #333;
}

.wrong-user-answer {
    color: #dc3545;
    font-size: 0.9rem;
}

.more-wrong-answers {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 1rem;
}

.reward-display {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin: 2rem 0;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.reward-display h3 {
    color: #333;
    margin-bottom: 1.5rem;
    text-align: center;
}

.rewards-list {
    display: grid;
    gap: 1rem;
}

.reward-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-radius: 10px;
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-left: 4px solid #28a745;
    animation: slideInRight 0.5s ease-out;
}

.reward-item.perfect {
    background: linear-gradient(45deg, #fff3cd, #ffeaa7);
    border-left-color: #ffc107;
}

.reward-item.excellent {
    background: linear-gradient(45deg, #d4edda, #c3e6cb);
    border-left-color: #28a745;
}

.reward-item.speed {
    background: linear-gradient(45deg, #cce5ff, #b3d9ff);
    border-left-color: #007bff;
}

.reward-item.streak {
    background: linear-gradient(45deg, #ffe6cc, #ffcc99);
    border-left-color: #fd7e14;
}

.reward-icon {
    font-size: 2rem;
    margin-right: 1rem;
}

.reward-text {
    flex: 1;
}

.reward-name {
    font-weight: bold;
    color: #333;
    margin-bottom: 0.2rem;
}

.reward-description {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.2rem;
}

.reward-points {
    color: #28a745;
    font-weight: bold;
    font-size: 0.9rem;
}

/* 滑入动画 */
@keyframes slideInRight {
    from {
        transform: translateX(100px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 移动端优化 */
@media (max-width: 768px) {
    .detail-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .wrong-answer-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .reward-item {
        flex-direction: column;
        text-align: center;
    }
    
    .reward-icon {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
}

.stat-item {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.stat-value {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: #333;
}

.result-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.action-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid white;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: white;
    color: #667eea;
}

.action-btn.primary {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    border-color: #4CAF50;
}

.action-btn.primary:hover {
    background: #45a049;
    color: white;
}

/* 错题本样式 */
.wrong-questions-list {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin: 2rem 0;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    max-height: 600px;
    overflow-y: auto;
}

.wrong-questions-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #eee;
}

.wrong-questions-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.stat-badge {
    text-align: center;
    padding: 1rem;
    border-radius: 10px;
    min-width: 100px;
}

.stat-badge.active {
    background: linear-gradient(45deg, #fff3cd, #ffeaa7);
    border: 2px solid #ffc107;
}

.stat-badge.mastered {
    background: linear-gradient(45deg, #d4edda, #c3e6cb);
    border: 2px solid #28a745;
}

.badge-number {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: #333;
}

.badge-label {
    display: block;
    font-size: 0.9rem;
    color: #666;
    margin-top: 0.5rem;
}

.wrong-questions-filter {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.filter-btn {
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.filter-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.filter-btn:hover:not(.active) {
    background: #e9ecef;
}

.wrong-questions-sort {
    text-align: center;
}

.wrong-questions-sort select {
    padding: 0.5rem 1rem;
    border: 2px solid #dee2e6;
    border-radius: 5px;
    background: white;
    font-size: 0.9rem;
}

.wrong-questions-container {
    max-height: 400px;
    overflow-y: auto;
}

.wrong-question-item {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 1rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-radius: 10px;
    background: #f8f9fa;
    border-left: 4px solid #dc3545;
    transition: all 0.3s ease;
}

.wrong-question-item.mastered {
    background: #f8fff8;
    border-left-color: #28a745;
    opacity: 0.8;
}

.wrong-question-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.question-main {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.question-expression {
    font-size: 1.3rem;
    font-weight: bold;
    color: #333;
}

.question-meta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.question-type,
.question-difficulty,
.question-category {
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
}

.question-difficulty {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
}

.question-category {
    background: rgba(40, 167, 69, 0.1);
    color: #155724;
}

.question-stats {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
    min-width: 150px;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
}

.stat-label {
    color: #666;
}

.stat-value {
    font-weight: bold;
    color: #333;
}

.stat-value.wrong {
    color: #dc3545;
}

.stat-value.skipped {
    color: #6c757d;
    font-style: italic;
}

.stat-value.mastered-time {
    color: #28a745;
}

.question-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 120px;
}

.practice-single-btn,
.mark-mastered-btn,
.delete-question-btn {
    padding: 0.4rem 0.8rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.practice-single-btn {
    background: #007bff;
    color: white;
}

.practice-single-btn:hover {
    background: #0056b3;
}

.mark-mastered-btn {
    background: #28a745;
    color: white;
}

.mark-mastered-btn:hover {
    background: #1e7e34;
}

.delete-question-btn {
    background: #dc3545;
    color: white;
}

.delete-question-btn:hover {
    background: #c82333;
}

.mastered-badge {
    background: #28a745;
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 5px;
    font-size: 0.8rem;
    text-align: center;
    font-weight: bold;
}

.empty-state {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 2rem;
}

.wrong-questions-actions {
    text-align: center;
    margin-top: 2rem;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .wrong-question-item {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .question-stats {
        min-width: auto;
    }
    
    .question-actions {
        flex-direction: row;
        justify-content: space-around;
        min-width: auto;
    }
    
    .wrong-questions-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .wrong-questions-filter {
        flex-wrap: wrap;
    }
    
    .question-meta {
        justify-content: flex-start;
    }
}

/* 进度界面样式 */
.progress-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.stat-card h3 {
    color: #666;
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #4CAF50;
}

.chart-container {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin: 2rem 0;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.achievements {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin: 2rem 0;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.achievements h3 {
    margin-bottom: 1rem;
    color: #333;
}

.achievements-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
}

.achievement-item {
    text-align: center;
    padding: 1rem;
    border-radius: 10px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.achievement-item.unlocked {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: white;
}

.achievement-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.achievement-name {
    font-size: 0.9rem;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .app-title {
        font-size: 2rem;
    }
    
    .question-text {
        font-size: 2rem;
    }
    
    .level-buttons {
        grid-template-columns: 1fr;
    }

    .exercise-settings {
        padding: 1rem;
    }

    .setting-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .setting-group label {
        min-width: auto;
    }
    
    .result-actions {
        flex-direction: column;
    }
    
    .action-btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .exercise-header {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .progress-section {
        width: 100%;
        min-width: auto;
        max-width: none;
    }

    .progress-bar {
        margin-right: 0;
        width: 100%;
    }

    .streak-display {
        width: auto;
        min-width: 60px;
    }

    .timer {
        width: auto;
        min-width: 70px;
    }
    
    .progress-text {
        position: static;
        margin-top: 0.5rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.view.active {
    animation: fadeIn 0.5s ease-out;
}

.feedback-message.correct {
    animation: correctGlow 0.6s ease-out;
}

/* 加载状态 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: scale(1); }
    40%, 43% { transform: scale(1.1); }
    70% { transform: scale(1.05); }
}

/* 连击显示样式 */
.streak-display {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.25);
    animation: pulse 0.5s ease-out;
    display: none;
    white-space: nowrap;
    min-width: 80px;
    width: 80px;
    text-align: center;
    border: 2px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
}

.streak-display.streak-high {
    background: linear-gradient(135deg, #ffd700, #ffb347);
    animation: streakGlow 0.6s ease-out;
    box-shadow: 0 4px 16px rgba(255, 215, 0, 0.4);
    border-color: rgba(255, 255, 255, 0.3);
}

.streak-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shine 2s infinite;
}

@keyframes shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 虚拟键盘样式 */
.virtual-keyboard {
    background: white;
    border-radius: 15px;
    padding: 1rem;
    margin-top: 1rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    display: none;
}

.keyboard-row {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.keyboard-row:last-child {
    margin-bottom: 0;
}

.key-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 60px;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.key-btn:hover {
    background: #e9ecef;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.key-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.key-backspace {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.key-backspace:hover {
    background: #c82333;
}

.key-enter {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.key-enter:hover {
    background: #218838;
}

/* 题目区域动画 */
.question-area {
    transition: all 0.3s ease-out;
}

/* 提示按钮样式 */
.hint-btn {
    background: rgba(255, 193, 7, 0.2);
    color: #856404;
    border: 2px solid #ffc107;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.hint-btn:hover {
    background: #ffc107;
    color: white;
}

/* 跳过按钮样式 */
.skip-btn {
    background: rgba(108, 117, 125, 0.2);
    color: #495057;
    border: 2px solid #6c757d;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
    margin-left: 0.5rem;
}

.skip-btn:hover {
    background: #6c757d;
    color: white;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .virtual-keyboard {
        display: block !important;
    }
    
    .key-btn {
        min-width: 50px;
        min-height: 50px;
        padding: 0.8rem;
        font-size: 1.1rem;
    }
    
    .keyboard-row {
        gap: 0.3rem;
    }
    
    .streak-display {
        position: static;
        margin-bottom: 1rem;
        display: inline-block;
    }
    
    .exercise-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .progress-bar {
        width: 100%;
        margin-right: 0;
    }
}

/* 脉冲动画 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* 温和的发光动画，替代晃动的bounce效果 */
@keyframes correctGlow {
    0% {
        box-shadow: 0 0 5px rgba(40, 167, 69, 0.3);
        background-color: #d4edda;
    }
    50% {
        box-shadow: 0 0 20px rgba(40, 167, 69, 0.6);
        background-color: #c3e6cb;
    }
    100% {
        box-shadow: 0 0 5px rgba(40, 167, 69, 0.3);
        background-color: #d4edda;
    }
}

@keyframes streakGlow {
    0% {
        box-shadow: 0 4px 16px rgba(255, 215, 0, 0.4);
        filter: brightness(1);
    }
    50% {
        box-shadow: 0 6px 24px rgba(255, 215, 0, 0.8);
        filter: brightness(1.1);
    }
    100% {
        box-shadow: 0 4px 16px rgba(255, 215, 0, 0.4);
        filter: brightness(1);
    }
}

/* 账号设置样式 */
.account-section {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin: 2rem 0;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.account-section h3 {
    color: #333;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid #eee;
    padding-bottom: 0.5rem;
}

.current-account {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #007bff;
}

.account-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.account-name {
    font-size: 1.2rem;
    font-weight: bold;
    color: #333;
}

.account-stats {
    font-size: 0.9rem;
    color: #666;
}

.edit-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.edit-btn:hover {
    background: #5a6268;
}

.account-actions, .data-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.action-btn.danger {
    background: #dc3545;
    border-color: #dc3545;
}

.action-btn.danger:hover {
    background: #c82333;
    border-color: #bd2130;
}

.accounts-list {
    max-height: 300px;
    overflow-y: auto;
}

.account-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.account-item.active {
    border-color: #007bff;
    background: #e3f2fd;
}

.account-item:hover {
    background: #e9ecef;
}

.account-item-info {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

.account-item-name {
    font-weight: bold;
    color: #333;
}

.account-item-stats {
    font-size: 0.8rem;
    color: #666;
}

.account-item-actions {
    display: flex;
    gap: 0.5rem;
}

.account-item-actions button {
    padding: 0.3rem 0.8rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.switch-btn {
    background: #007bff;
    color: white;
}

.switch-btn:hover {
    background: #0056b3;
}

.delete-btn {
    background: #dc3545;
    color: white;
}

.delete-btn:hover {
    background: #c82333;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.modal-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-content h3 {
    margin-bottom: 1rem;
    color: #333;
}

.modal-content input {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid #dee2e6;
    border-radius: 5px;
    font-size: 1rem;
    margin-bottom: 1rem;
    box-sizing: border-box;
}

.modal-content input:focus {
    outline: none;
    border-color: #007bff;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.modal-actions button {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.modal-actions .primary {
    background: #007bff;
    color: white;
}

.modal-actions .primary:hover {
    background: #0056b3;
}

.modal-actions .secondary {
    background: #6c757d;
    color: white;
}

.modal-actions .secondary:hover {
    background: #5a6268;
}