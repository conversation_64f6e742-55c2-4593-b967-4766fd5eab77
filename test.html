<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数学训练 - 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        h1 { color: #333; text-align: center; }
        h2 { color: #666; border-bottom: 2px solid #eee; padding-bottom: 10px; }
    </style>
</head>
<body>
    <h1>数学训练系统 - 功能测试</h1>
    
    <div class="test-section">
        <h2>数据模型测试</h2>
        <button class="test-button" onclick="runDataModelTests()">运行数据模型测试</button>
        <div id="model-test-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>练习系统测试</h2>
        <button class="test-button" onclick="runExerciseSystemTests()">运行练习系统测试</button>
        <div id="exercise-test-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>错题管理测试</h2>
        <button class="test-button" onclick="runWrongQuestionsTests()">运行错题管理测试</button>
        <div id="wrong-questions-test-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>存储系统测试</h2>
        <button class="test-button" onclick="testStorageSystem()">测试存储系统</button>
        <div id="storage-test-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>题目生成测试</h2>
        <button class="test-button" onclick="testQuestionGeneration()">测试题目生成</button>
        <div id="question-test-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>集成测试</h2>
        <button class="test-button" onclick="runIntegrationTestsFunc()">运行集成测试</button>
        <div id="integration-test-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>端到端测试</h2>
        <button class="test-button" onclick="runE2ETestsFunc()">运行端到端测试</button>
        <div id="e2e-test-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>应用启动测试</h2>
        <button class="test-button" onclick="window.open('index.html', '_blank')">打开主应用</button>
        <p>点击上方按钮打开主应用进行手动测试</p>
    </div>

    <!-- 加载所有脚本 -->
    <script src="js/models/DataModels.js"></script>
    <script src="js/storage/StorageManager.js"></script>
    <script src="js/managers/QuestionBankManager.js"></script>
    <script src="js/managers/ExerciseManager.js"></script>
    <script src="js/managers/WrongQuestionManager.js"></script>
    <script src="js/managers/ProgressTracker.js"></script>
    <script src="js/managers/RewardSystem.js"></script>
    <script src="tests/models.test.js"></script>
    <script src="tests/exercise.test.js"></script>
    <script src="tests/wrong-questions.test.js"></script>
    <script src="tests/integration.test.js"></script>
    <script src="tests/e2e.test.js"></script>
    <script src="js/controllers/AppController.js"></script>

    <script>
        function runDataModelTests() {
            const resultDiv = document.getElementById('model-test-result');
            resultDiv.innerHTML = '运行中...';
            
            try {
                const success = runModelTests();
                resultDiv.className = success ? 'test-result success' : 'test-result error';
                resultDiv.innerHTML = success ? '✅ 所有数据模型测试通过' : '❌ 部分数据模型测试失败，请查看控制台';
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ 测试运行失败: ' + error.message;
            }
        }

        function runExerciseSystemTests() {
            const resultDiv = document.getElementById('exercise-test-result');
            resultDiv.innerHTML = '运行中...';
            
            try {
                const success = runExerciseTests();
                resultDiv.className = success ? 'test-result success' : 'test-result error';
                resultDiv.innerHTML = success ? '✅ 所有练习系统测试通过' : '❌ 部分练习系统测试失败，请查看控制台';
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ 测试运行失败: ' + error.message;
            }
        }

        function runWrongQuestionsTests() {
            const resultDiv = document.getElementById('wrong-questions-test-result');
            resultDiv.innerHTML = '运行中...';
            
            try {
                const success = runWrongQuestionsTests();
                resultDiv.className = success ? 'test-result success' : 'test-result error';
                resultDiv.innerHTML = success ? '✅ 所有错题管理测试通过' : '❌ 部分错题管理测试失败，请查看控制台';
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ 测试运行失败: ' + error.message;
            }
        }

        function testStorageSystem() {
            const resultDiv = document.getElementById('storage-test-result');
            resultDiv.innerHTML = '测试中...';
            
            try {
                // 测试存储功能
                const testData = { test: 'data', number: 123, array: [1, 2, 3] };
                const success = storageManager.setItem('test_key', testData);
                
                if (!success) {
                    throw new Error('存储数据失败');
                }
                
                const retrieved = storageManager.getItem('test_key');
                if (JSON.stringify(retrieved) !== JSON.stringify(testData)) {
                    throw new Error('检索数据不匹配');
                }
                
                // 清理测试数据
                storageManager.removeItem('test_key');
                
                // 测试存储信息
                const info = storageManager.getStorageInfo();
                
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `✅ 存储系统正常<br>
                    支持: ${storageManager.isSupported ? 'localStorage' : '内存存储'}<br>
                    使用量: ${info.percentage}%`;
                    
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ 存储系统测试失败: ' + error.message;
            }
        }

        function testQuestionGeneration() {
            const resultDiv = document.getElementById('question-test-result');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const results = [];
                
                // 测试各种题库
                const levels = ['within-10', 'within-20', 'within-100', 'multiplication'];
                
                levels.forEach(level => {
                    const questions = questionBankManager.generateQuestions(level, 10);
                    const valid = questions.every(q => 
                        questionBankManager.validateQuestion(q, level)
                    );
                    
                    results.push(`${level}: ${valid ? '✅' : '❌'} (${questions.length}题)`);
                });
                
                // 测试题库信息
                const banks = questionBankManager.getAvailableBanks();
                results.push(`可用题库: ${banks.length}个`);
                
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = results.join('<br>');
                
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ 题目生成测试失败: ' + error.message;
            }
        }

        async function runIntegrationTestsFunc() {
            const resultDiv = document.getElementById('integration-test-result');
            resultDiv.innerHTML = '运行中...';
            
            try {
                const success = await integrationTest.run();
                resultDiv.className = success ? 'test-result success' : 'test-result error';
                resultDiv.innerHTML = success ? '✅ 所有集成测试通过' : '❌ 部分集成测试失败，请查看控制台';
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ 集成测试运行失败: ' + error.message;
            }
        }

        async function runE2ETestsFunc() {
            const resultDiv = document.getElementById('e2e-test-result');
            resultDiv.innerHTML = '运行中...';
            
            try {
                // 初始化应用控制器（如果还没有）
                if (typeof appController === 'undefined') {
                    window.appController = new AppController();
                    appController.init();
                }
                
                const success = await e2eTest.run();
                resultDiv.className = success ? 'test-result success' : 'test-result error';
                resultDiv.innerHTML = success ? '✅ 所有端到端测试通过' : '❌ 部分端到端测试失败，请查看控制台';
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ 端到端测试运行失败: ' + error.message;
            }
        }

        // 页面加载完成后显示系统信息
        window.addEventListener('load', () => {
            console.log('数学训练系统测试页面已加载');
            console.log('系统信息:', {
                localStorage支持: storageManager.isSupported,
                可用题库: questionBankManager.getAvailableBanks().length,
                当前时间: new Date().toLocaleString()
            });
        });
    </script>
</body>
</html>